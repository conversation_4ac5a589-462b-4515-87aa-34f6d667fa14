# Ignore Python bytecode files
__pycache__/
*.py[cod]
*$py.class

# Ignore environment variable files
.env
.env.*

# Ignore virtual environments
venv/
.venv/

# Ignore pipenv files
Pipfile.lock

# Ignore poetry files
poetry.lock

# Ignore Python egg files
*.egg
*.egg-info/
dist/
build/
eggs/
*.egg-info/
.installed.cfg
lib/
lib64/

# Ignore PyInstaller files
# Usually contains some form of binary
*.manifest
*.spec

# Ignore coverage files
.coverage
.tox/
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Ignore test result files
*.pytest_cache/
*.pytest_cache/*
junitxml.xml

# Ignore Jupyter Notebook checkpoints
.ipynb_checkpoints

# Ignore mypy cache files
.mypy_cache/
.dmypy.json
dmypy.json

# Ignore PyCharm IDE settings
.idea/

# Ignore VS Code settings
.vscode/

# Ignore local dev environment settings
local_settings.py

# Ignore logs and databases
*.log
*.sqlite3

# Ignore any temporary or system files
*.swp
*.swo
.DS_Store
Thumbs.db