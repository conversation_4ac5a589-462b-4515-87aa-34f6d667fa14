# firebase-beastcraft

This is the firebase backend for BeastCraft.

## Setup
1. clone the repo
2. install firebase CLI
3. install node and npm
4. navigate to functions folder and run `npm install` to install all dependencies in the functions folder

## Deploy
1. Deploy dev:`npm run deploy:dev`
2. Deploy prod:`npm run deploy:prod`

## Debugging
1. Run `firebase emulators:start`

### Troubleshooting debugging
Generating signed URLs is a bit of a pain. Easier to deploy function and test that. 

# Tips

## Creating .env files
We are using 2nd gen to create a .env file in the root directory.
For this, you can follow [this](https://firebase.google.com/docs/functions/config-env?gen=2nd) guide.
In your code you basically only have to do this:

```js
const { onRequest } = require('firebase-functions/v2/https');
const { defineInt, defineString } = require('firebase-functions/params');

// Define some parameters
const minInstancesConfig = defineInt('HELLO_WORLD_MININSTANCES');
const welcomeMessage = defineString('WELCOME_MESSAGE');
```
Firebase will automatically create the .env file for you and ask you to define the parameters.

## Generating signed URLs
You have to make sure the service account you are using has the correct permissions.
For me, I had to go to Google Cloud IAM $ Admin page and give "Service Account Token Creator" permission to the "Default compute service account". 