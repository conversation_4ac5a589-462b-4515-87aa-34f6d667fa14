const admin = require('firebase-admin');
const {getAppVersionOrDefault} = require('./shared');
const logger = require("firebase-functions/logger");

async function getLevelList(req, res) {
    if (req.method !== 'POST') {
        return res.status(405).send({message: 'Method Not Allowed'});
    }

    try {
        const {levelIdList} = req.body;
        const appVersion = getAppVersionOrDefault(req);
        const defaultLimit = 50;
        let levelList = [];

        if (levelIdList && levelIdList.length > 0) {
            const requiredLevelList = await getRequiredLevelList(levelIdList);
            if (requiredLevelList && requiredLevelList.length > 0) {
                levelList = requiredLevelList;
            }
        }

        const limit = levelList.length >= defaultLimit ? 0 : defaultLimit - levelList.length;

        if (limit > 0) {
            const snapshot = await admin.firestore().collection('maps')
                .where('isPublished', '==', true)
                .where('appVersion', '<=', appVersion)
                .orderBy('updatedAt', 'desc')
                .select('name', 'gameMode', 'createdByName', 'worldTemplateId')
                .limit(limit)
                .get();

            snapshot.forEach(doc => {
                levelList.push(doc.data());
            });
        }

        res.status(200).send({data: levelList});
    } catch (error) {
        logger.error(error);
        res.status(500).send({message: 'Internal Server Error'});
    }
}

async function getRequiredLevelList(levelIdList) {
    try {
        const snapshot = await admin.firestore()
            .collection('maps')
            .where(admin.firestore.FieldPath.documentId(), 'in', levelIdList)
            .select('name', 'gameMode', 'createdByName', 'worldTemplateId')
            .get();

        const requiredLevelList = [];
        snapshot.forEach(doc => {
            requiredLevelList.push(doc.data());
        });

        return requiredLevelList;
    } catch (error) {
        logger.error(error);
        return null;
    }
}

module.exports = getLevelList;