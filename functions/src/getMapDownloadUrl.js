const admin = require('firebase-admin');
const logger = require("firebase-functions/logger");

async function getMapDownloadUrl(req, res) {
    try {
        const {path} = req.query;
        if (!path) {
            return res.status(400).send({message: 'Path required'});
        }

        const url = await getUrl(`maps/${path}.bytes`);
        if (url === null) {
            return res.status(404).send({message: 'Url not found'});
        }

        res.status(200).send(url.toString());
    } catch (error) {
        logger.error(error);
        res.status(500).send({message: error});
    }
}

async function getUrl(filePath) {
    const bucket = admin.storage().bucket();
    const file = bucket.file(filePath);
    const [exists] = await file.exists();
    if (exists) {
        const options = {
            version: 'v4', action: 'read', expires: Date.now() + 15 * 60 * 1000
        };
        const [url] = await file.getSignedUrl(options);
        return url;
    }
    return null;
}

module.exports = getMapDownloadUrl;
