const admin = require('firebase-admin');
const {checkProfanity} = require('./checkProfanity');
const logger = require("firebase-functions/logger");

async function createLevel(req, res) {
    if (req.method !== 'POST') {
        return res.status(405).send({message: 'Method Not Allowed'});
    }

    try {
        const {name, createdByName, isPublished, password, buildingDisabled, gameMode, disabledLocomotions, worldTemplateId, worldExtents} = req.body;
        const createdBy = req.unityId;
        const appVersionText = req.header('X-Remio-Version');

        if (!name) {
            return res.status(400).send({message: 'Level name is required'});
        }

        if (checkProfanity(name)) {
            return res.status(400).send({message: 'Level name contains profanity'});
        }

        const levelId = name.replace(/\s+/g, '_').toLowerCase();
        const collectionName = 'maps';
        const levelDocRef = admin.firestore().collection(collectionName).doc(levelId);
        const levelDoc = await levelDocRef.get();

        if (levelDoc.exists) {
            return res.status(409).send({message: 'Level with this name already exists'});
        }

        const appVersionNumber = getAppVersionNumber(appVersionText);
        const now = new Date().toISOString();

        const levelData = {
            name,
            createdBy,
            createdByName,
            worldTemplateId,
            worldExtents,
            appVersion: appVersionNumber || 0,
            isPublished: isPublished,
            password: password || '',
            gameMode: gameMode,
            buildingDisabled: buildingDisabled || false,
            disabledLocomotions: disabledLocomotions || 0,
            createdAt: now,
            updatedAt: now,
            upvotes: 0
        };

        await levelDocRef.set(levelData);
        levelData.id = levelId;
        res.status(201).send({data: levelData});
    } catch (error) {
        logger.error(error);
        res.status(500).send({message: 'Internal Server Error'});
    }
}

function getAppVersionNumber(appVersionText) {
    if (!appVersionText) {
        return null;
    }

    const parts = appVersionText.split('.');
    if (parts.length !== 3) {
        return null;
    }

    const appVersionNumber = parseFloat(parts[0] + "." + parts[1]);

    if (isNaN(appVersionNumber)) {
        return null;
    }

    return appVersionNumber;
}

module.exports = createLevel;