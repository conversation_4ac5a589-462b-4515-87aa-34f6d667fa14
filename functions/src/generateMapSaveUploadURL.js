const admin = require('firebase-admin');
const logger = require("firebase-functions/logger");

async function generateMapSaveUploadURL(req, res) {
    if (req.method !== 'POST') {
        return res.status(405).send({message: 'Method Not Allowed'});
    }

    const { id } = req.body;
    if (!id) {
        return res.status(400).send({message: 'Map id is required'});
    }

    try {
        const filePath = `maps/save/${id}.bytes`;

        const options = {
            version: 'v4',
            action: 'write',
            expires: Date.now() + 15 * 60 * 1000, // URL valid for 15 minutes
            contentType: 'application/octet-stream',
        };

        const bucket = admin.storage().bucket();
        const uploadUrl = await bucket.file(filePath).getSignedUrl(options);

        res.status(200).send(uploadUrl.toString());
    } catch (error) {
        logger.error('Error generating upload URL:', error);
        res.status(500).send({message: 'Internal Server Error'});
    }
}

module.exports = generateMapSaveUploadURL;
