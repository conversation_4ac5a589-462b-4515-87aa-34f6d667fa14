const admin = require('firebase-admin');
const logger = require("firebase-functions/logger");

async function getMapUploadUrl(req, res) {
    try {
        const {path} = req.query;
        if (!path) {
            return res.status(400).send({message: 'Path required'});
        }

        const options = {
            version: 'v4',
            action: 'write',
            expires: Date.now() + 15 * 60 * 1000,
            contentType: 'application/octet-stream',
        };
        const bucket = admin.storage().bucket();
        const uploadUrl = await bucket.file(`maps/${path}.bytes`).getSignedUrl(options);

        res.status(200).send(uploadUrl.toString());
    } catch (error) {
        logger.error(error);
        res.status(500).send({message: error});
    }
}

module.exports = getMapUploadUrl;
