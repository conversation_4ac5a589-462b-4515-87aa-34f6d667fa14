const admin = require('firebase-admin');
const logger = require("firebase-functions/logger");

async function getLobbyLevelList(req, res) {
    try {
        if (req.method !== 'POST') {
            return res.status(405).send({message: 'Method Not Allowed'});
        }

        const {levelIdList} = req.body;
        const levelList = [];

        if (levelIdList && levelIdList.length > 0) {
            const snapshot = await admin.firestore()
                .collection('maps')
                .where(admin.firestore.FieldPath.documentId(), 'in', levelIdList)
                .select('name', 'gameMode', 'worldTemplateId')
                .get();

            snapshot.forEach(doc => {
                levelList.push(doc.data());
            });
        }

        res.status(200).send({data: levelList});
    } catch (error) {
        logger.error(error);
        res.status(500).send({message: 'Internal Server Error'});
    }
}

module.exports = getLobbyLevelList;