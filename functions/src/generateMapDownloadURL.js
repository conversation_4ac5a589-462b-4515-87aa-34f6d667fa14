const admin = require('firebase-admin');
const logger = require("firebase-functions/logger");

async function generateMapDownloadURL(req, res) {
    const { id, instanceId } = req.query;
    if (!id) {
        return res.status(400).send({message: 'Map id is required'});
    }

    try {
        let downloadUrl = null;
        if (instanceId) {
            downloadUrl = await getSignedUrlIfFileExists(`maps/runtime/${id}/${instanceId}.bytes`);
        }
        
        if (downloadUrl === null) {
            downloadUrl = await getSignedUrlIfFileExists(`maps/save/${id}.bytes`);
        }
        
        if (downloadUrl === null) {
            return res.status(404).send({message: 'Map not found'});
        }

        res.status(200).send(downloadUrl.toString());
    } catch (error) {
        logger.error('Error generating download URL:', error);
        res.status(500).send({message: 'Internal Server Error'});
    }
}

async function getSignedUrlIfFileExists(filePath) {
    const bucket = admin.storage().bucket();
    const file = bucket.file(filePath);
    const [exists] = await file.exists();
    if (exists) {
        const options = {
            version: 'v4',
            action: 'read',
            expires: Date.now() + 15 * 60 * 1000, // URL valid for 15 minutes
        };
        const [url] = await file.getSignedUrl(options);
        return url;
    }
    return null;
}

module.exports = generateMapDownloadURL;
