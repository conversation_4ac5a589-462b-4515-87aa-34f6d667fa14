const admin = require('firebase-admin');
const logger = require("firebase-functions/logger");

async function setAppVersion(req, res) {
    if (req.method !== 'POST') {
        res.status(405).send('Only POST method is allowed');
        return;
    }

    const { version } = req.body;
    const versionRegex = /^\d+\.\d+\.\d+$/;

    if (!version) {
        res.status(400).send('No version specified');
        return;
    }

    if (!versionRegex.test(version)) {
        res.status(400).send('Version format is invalid. Expected format: major.minor.bundlecode');
        return;
    }

    try {
        const configRef = admin.firestore().collection('config').doc('version');
        await configRef.set({ current: version }, { merge: true });
        res.status(200).send(`App version updated to ${version}`);
    } catch (error) {
        logger.error('Error setting app version:', error);
        res.status(500).send('Failed to update app version');
    }
}

module.exports = setAppVersion;