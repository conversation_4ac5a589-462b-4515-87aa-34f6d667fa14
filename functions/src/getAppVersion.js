const admin = require('firebase-admin');
const logger = require("firebase-functions/logger");

async function getAppVersion(req, res) {
    if (req.method !== 'GET') {
        res.status(405).send('Only GET method is allowed');
        return;
    }

    try {
        const configRef = admin.firestore().collection('config').doc('version');
        const doc = await configRef.get();

        if (!doc.exists) {
            res.status(404).send('App version document does not exist');
            return;
        }

        const { current } = doc.data();
        res.status(200).json({ version: current });
    } catch (error) {
        logger.error('Error getting app version:', error);
        res.status(500).send('Failed to retrieve app version');
    }
}

module.exports = getAppVersion;