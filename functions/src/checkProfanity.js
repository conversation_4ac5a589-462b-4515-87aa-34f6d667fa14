const fs = require('fs');
const path = require('path');
const logger = require("firebase-functions/logger");

const profanityList = JSON.parse(
    fs.readFileSync(path.join(__dirname, 'profanityWordlist.json'), 'utf8')
);

async function checkProfanityHandler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).send('Method Not Allowed');
  }

  const { text } = req.body;
  if (!text || typeof text !== 'string') {
    return res.status(400).send('The "text" parameter is required and must be a string');
  }

  try {
    const containsProfanity = checkProfanity(text);

    res.status(200).send({ containsProfanity });
  } catch (error) {
    logger.error(error);
    res.status(500).send('Internal Server Error');
  }
}

function checkProfanity(text) {
  return profanityList.some((word) => text.toLowerCase().includes(word.toLowerCase()));
}

module.exports = { checkProfanity, checkProfanityHandler }; 