const jwt = require('jsonwebtoken');
const {defineString} = require("firebase-functions/params");
const logger = require("firebase-functions/logger");

const SECRET_KEY_PARAM = defineString('SECRET_KEY');
const UNITY_PROJECT_ID_PARAM = defineString('UNITY_PROJECT_ID');
const UNITY_PROJECT_NAME_PARAM = defineString('UNITY_PROJECT_NAME');

async function getToken(req, res) {
    const { unityId } = req.body;

    if (unityId === undefined || typeof unityId !== 'string' || unityId.trim().length < 10) {
        return res.status(400).send({ message: 'unityId is required or is invalid' });
    }

    try {
        const SECRET_KEY = SECRET_KEY_PARAM.value();
        if (!SECRET_KEY) {
            throw new Error('SECRET_KEY is not defined');
        }
        
        // Create a token with claims
        const token = jwt.sign(
            {
                unityId, 
            },
            SECRET_KEY, 
            { expiresIn: '48h' } 
        );

        res.status(200).send(token.toString());
    } catch (error) {
        logger.error('Error generating token:', error);
        res.status(500).send({ message: 'Failed to generate token' });
    }
}

async function getUnityPlayerIdFromOculusId(oculusId) {
    if (!oculusId) {
        throw new Error('No oculusId provided');
    }

    const UNITY_PROJECT_ID = UNITY_PROJECT_ID_PARAM.value();
    if (!UNITY_PROJECT_ID) {
        throw new Error('UNITY_PROJECT_ID is not defined');
    }
    const UNITY_PROJECT_NAME = UNITY_PROJECT_NAME_PARAM.value();
    if (!UNITY_PROJECT_NAME) {
        throw new Error('UNITY_PROJECT_NAME is not defined');
    }

    logger.log('Verifying user with oculusId exists:', oculusId);
    logger.log('Project ID:', UNITY_PROJECT_ID);
    logger.log('Project Name:', UNITY_PROJECT_NAME);

    const UNITY_AUTH_URL = 'https://player-auth.services.api.unity.com/v1/authentication/usernamepassword/sign-in';

    try {
        const signInResponse = await fetch(UNITY_AUTH_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'ProjectId': UNITY_PROJECT_ID,
                'UnityEnvironment': UNITY_PROJECT_NAME,
            },
            body: JSON.stringify({
                username: oculusId,
                password: `${oculusId}_Pw!`
            })
        });

        if (!signInResponse.ok) {
            const errorText = await signInResponse.text();
            logger.error('Failed to verify session token:', errorText);
            throw new Error(errorText);
        }

        const verificationResult = await signInResponse.json();
        logger.log('Verification result:', verificationResult);
        return verificationResult.userId;
    } catch (error) {
        logger.error('Error verifying session token:', error);
        throw new Error('Error verifying session token');
    }
}

module.exports = getToken;
