const jwt = require('jsonwebtoken');
const {defineString} = require('firebase-functions/params');
const logger = require("firebase-functions/logger");

const SECRET_KEY_PARAM = defineString('SECRET_KEY');

function withAuthentication(handler) {
    return async (req, res) => {
        try {
            const token = req.headers.token;
            if (!token) {
                throw new Error('Access Denied. No token provided.');
            }

            // Verify the token
            const SECRET_KEY = SECRET_KEY_PARAM.value();
            if (!SECRET_KEY) {
                throw new Error('SECRET_KEY is not defined');
            }
            const decoded = jwt.verify(token, SECRET_KEY);

            // Attach the decoded token to the request for use in handlers
            req.unityId = decoded.unityId;

            // Proceed to the original handler
            return handler(req, res);
        } catch (error) {
            logger.error('Authentication: ', error.message);
            return res.status(401).send({message: 'Unauthorized'});
        }
    };
}

module.exports = {withAuthentication};