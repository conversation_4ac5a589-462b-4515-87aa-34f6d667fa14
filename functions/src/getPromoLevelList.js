const admin = require('firebase-admin');
const {getAppVersionOrDefault} = require('./shared');
const logger = require("firebase-functions/logger");

async function getPromoLevelList(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).send({message: 'Method Not Allowed'});
    }

    try {
        const appVersion = getAppVersionOrDefault(req);
        const promoLevelList = [];

        const snapshot = await admin.firestore().collection('maps')
            .where('isPublished', '==', true)
            .where('appVersion', '<=', appVersion)
            .where('isPromo', '==', true)
            .select('name', 'gameMode', 'createdByName', 'worldTemplateId')
            .get();

        snapshot.forEach(doc => {
            promoLevelList.push(doc.data());
        });

        res.status(200).send({data: promoLevelList});
    } catch (error) {
        logger.error(error);
        res.status(500).send({message: 'Internal Server Error'});
    }
}

module.exports = getPromoLevelList;