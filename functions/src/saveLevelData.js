const admin = require('firebase-admin');
const logger = require("firebase-functions/logger");

async function saveLevelDataHandler(req, res) {
    if (req.method !== 'POST' && req.method !== 'PUT') {
        return res.status(405).send({message: 'Method Not Allowed'});
    }

    const id = req.query.id;
    const inputData = req.body;

    if (!id) {
        return res.status(400).send({message: 'Level id is required'});
    }
    const playerId = req.unityId;

    try {
        const collectionName = 'maps';
        const levelDocRef = admin.firestore().collection(collectionName).doc(id);
        const levelDoc = await levelDocRef.get();

        if (!levelDoc.exists) {
            return res.status(404).send({message: 'Level does not exist. Please create the level first.'});
        }

        const levelData = levelDoc.data();
        if (playerId !== levelData.createdBy) {
            return res.status(403).send({message: 'Access denied. You are not the creator of this level.'});
        }

        const updateData = {
            updatedAt: new Date().toISOString()
        };
        Object.entries(inputData).forEach(([key, value]) => {
            if (value !== undefined) {
                updateData[key] = value;
            }
        });

        if (Object.keys(updateData).length === 0) {
            return res.status(400).send({message: 'No data to update'});
        }

        await levelDocRef.update(updateData);
        res.status(200).send({message: 'Level data saved successfully'});
    } catch (error) {
        logger.error(error);
        res.status(500).send({message: 'Internal Server Error'});
    }
}

module.exports = saveLevelDataHandler;
