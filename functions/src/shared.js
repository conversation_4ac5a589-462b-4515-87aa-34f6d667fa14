function getAppVersionOrDefault(req) {
    const defaultAppVersion = 100;

    if (!req || !req.header) {
        return defaultAppVersion;
    }

    const appVersionText = req.header('X-Remio-Version')

    if (!appVersionText) {
        return defaultAppVersion;
    }

    const parts = appVersionText.split('.');
    if (parts.length !== 3) {
        return defaultAppVersion;
    }

    const appVersion = parseFloat(parts[0] + "." + parts[1]);

    if (isNaN(appVersion)) {
        return defaultAppVersion;
    }

    return appVersion;
}

module.exports = {getAppVersionOrDefault};