const admin = require('firebase-admin');
const logger = require("firebase-functions/logger");

async function getLevelData(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).send({message: 'Method Not Allowed'});
    }
    
    const playerId = req.playerId;

    const documentId = req.query.id;
    if (!documentId) {
        return res.status(400).send({message: 'Level id is required'});
    }
    
    const password = req.query.password;
    try {
        const collectionName = 'maps';
        const levelDocRef = admin.firestore().collection(collectionName).doc(documentId);
        const levelDoc = await levelDocRef.get();

        if (!levelDoc.exists) {
            return res.status(404).send({message: 'Level not found'});
        }

        const levelData = levelDoc.data();
        if (levelData.createdBy === playerId || isPasswordCorrect(levelData, password)) {
            levelData.id = documentId;
            res.status(200).send({data: levelData});
            return;
        }
        
        res.status(403).send({message: 'Access denied'});
    } catch (error) {
        logger.error(error);
        res.status(500).send({message: 'Internal Server Error'});
    }
}

function isPasswordCorrect(levelData, password) {
    if (levelData.isPublished)
    {
        return true;
    }
    
    if (levelData.password === undefined || levelData.password === '') {
        return true;
    }
    
    return levelData.password === password;
}

module.exports = getLevelData;
