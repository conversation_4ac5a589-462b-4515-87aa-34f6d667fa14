let admin = require("firebase-admin");

// let serviceAccount = require("/Users/<USER>/apps/beastcraft-prod-firebase-adminsdk-mbumb-a076b30064.json");
let serviceAccount = require("/Users/<USER>/apps/beastcraft-dev-firebase-adminsdk-fn1ms-e219297376.json");

admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
});

const db = admin.firestore();

const newMapData = {
    "appVersion": 2.172,
    "buildingDisabled": true,
    "createdAt": "202ddw5-05-05T23:42:04.190Z",
    "createdBy": "jibhySO4368XA6yxW5OOPZRpZjQ0",
    "createdByName": "dirkazoid",
    "disabledLocomotions": 15,
    "gameMode": 5,
    "name": "The Mines",
    "password": "",
    "updatedAt": "2025-05-05T23:42:04.190Z",
    "upvotes": 0,
    "worldExtents": 125,
    "worldTemplateId": 13,
    "monsters": [
        {
            "code": 0,
            "spawn": {
                "x": 34,
                "y": 12,
                "z": 54
            },
            "rotation": 0
        },
        {
            "code": 2,
            "spawn": {
                "x": -74,
                "y": 2,
                "z": 72
            },
            "rotation": 0
        },
        {
            "code": 2,
            "spawn": {
                "x": -74,
                "y": 2,
                "z": 54
            },
            "rotation": 0
        },
        {
            "code": 2,
            "spawn": {
                "x": -74,
                "y": 2,
                "z": 38
            },
            "rotation": 0
        }
    ]
}

const mapId = "the_mines";

(async () => {
    try {
        const docRef = db.collection('maps').doc(mapId);

        await docRef.create(newMapData);

        console.log(`Document ${mapId} updated.`);

        console.log('All documents updated successfully!');
    } catch (error) {
        console.error('Error:', error);
    }
})();