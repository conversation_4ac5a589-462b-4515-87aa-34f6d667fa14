let admin = require("firebase-admin");

// let serviceAccount = require("/Users/<USER>/apps/beastcraft-prod-firebase-adminsdk-mbumb-a076b30064.json");
let serviceAccount = require("/Users/<USER>/apps/beastcraft-dev-firebase-adminsdk-fn1ms-e219297376.json");

admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
});

const db = admin.firestore();

const mapTemplates = {
    "YoshiHorrorWorld": 1,
    "YoshiExpandedWorld": 2,
    "YoshiWorld": 3,
    "YoshiAquaticWorld": 4,
    "YoshiLavaWorld": 5,
    "FlatWorld": 6
};

const gameModes = {
    "sandbox": 0,
    "infection": 1,
    "infectionzombie": 2,
    "survival": 3,
    "survivalzombie": 4,
    "buildonly": 0
};

const newMapData = {
    "monsters": [
        {
            "code": 3,
            "spawn": {
                "x": 17,
                "y": 32,
                "z": -25
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": -18,
                "y": 32,
                "z": -24
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": -45,
                "y": 32,
                "z": -24
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": -48.5,
                "y": 32,
                "z": 14
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": -48.5,
                "y": 32,
                "z": 14
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": -33,
                "y": 32,
                "z": 35
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": -33,
                "y": 32,
                "z": 35
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": -3,
                "y": 32,
                "z": 35
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": -3,
                "y": 32,
                "z": 35
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": -10,
                "y": 32,
                "z": 3
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": -10,
                "y": 32,
                "z": 3
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": -10,
                "y": 32,
                "z": 3
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": 11,
                "y": 32,
                "z": 25
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": 11,
                "y": 32,
                "z": 25
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": 11,
                "y": 32,
                "z": 25
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": 25,
                "y": 32,
                "z": 15
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": 41,
                "y": 32,
                "z": 15
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": 31,
                "y": 32,
                "z": 45
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": 31,
                "y": 32,
                "z": 45
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": 11,
                "y": 25,
                "z": 25
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": 23,
                "y": 25,
                "z": 15
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": -9,
                "y": 25,
                "z": 15
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": -9,
                "y": 25,
                "z": 15
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": -9,
                "y": 25,
                "z": -5
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": 13,
                "y": 25,
                "z": -5
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": 31,
                "y": 25,
                "z": -14
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": 31,
                "y": 25,
                "z": -14
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": 31,
                "y": 25,
                "z": -31
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": 31,
                "y": 25,
                "z": -31
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": 12,
                "y": 25,
                "z": -25
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": 12,
                "y": 25,
                "z": -25
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": -14,
                "y": 25,
                "z": -25
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": -14,
                "y": 25,
                "z": -25
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": -42,
                "y": 25,
                "z": -25
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": -42,
                "y": 25,
                "z": -25
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": -48,
                "y": 25,
                "z": -46
            },
            "rotation": 0
        },
        {
            "code": 3,
            "spawn": {
                "x": -48,
                "y": 25,
                "z": -46
            },
            "rotation": 0
        },
        {
            "code": 4,
            "spawn": {
                "x": -4,
                "y": 23.5,
                "z": -25
            },
            "patrolCenter":
                {
                    "x": 4.5,
                    "y": 24.5,
                    "z": -20
                },
            "patrolArea":
                {
                    "x": 113,
                    "y": 10,
                    "z": 62
                }
        }
    ]
}

const mapId = "the_mines_2";

(async () => {
    try {
        const docRef = db.collection('maps').doc(mapId);

        await docRef.update(newMapData);

        console.log(`Document ${mapId} updated.`);
    } catch (error) {
        console.error('Error:', error);
    }
})();