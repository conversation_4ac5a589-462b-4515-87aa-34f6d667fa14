let admin = require("firebase-admin");

let serviceAccountProd = require("/Users/<USER>/apps/beastcraft-prod-firebase-adminsdk-mbumb-a076b30064.json");
// let serviceAccountDev = require("/Users/<USER>/apps/beastcraft-dev-firebase-adminsdk-fn1ms-e219297376.json");

admin.initializeApp({
    credential: admin.credential.cert(serviceAccountProd)
});

const db = admin.firestore();

const mapId = "the_mines";
const mapId2 = "the_mines_2";

(async () => {
    try {
        const docRef = db.collection('maps').doc(mapId);
        docRef.get().then(async (doc) => {
            if (doc.exists) {
                console.log("Document data:", doc.data());
                let newMap = doc.data();
                newMap.name = "The Mines 2";
                const newdocRef = db.collection('maps').doc(mapId2);

                await newdocRef.create(newMap);

            } else {
                console.log("No such document!");
            }
        }).catch((error) => {
            console.log("Error getting document:", error);
        });
    } catch (error) {
        console.error('Error:', error);
    }
})();