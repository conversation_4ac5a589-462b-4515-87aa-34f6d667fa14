let admin = require("firebase-admin");

let serviceAccountProd = require("/Users/<USER>/apps/beastcraft-prod-firebase-adminsdk-mbumb-a076b30064.json");
let serviceAccountDev = require("/Users/<USER>/apps/beastcraft-dev-firebase-adminsdk-fn1ms-e219297376.json");

admin.initializeApp({
    credential: admin.credential.cert(serviceAccountDev)
});

const db = admin.firestore();

// const mapId = "speedrun_2";
//
// (async () => {
//     try {
//         const docRef = db.collection('maps').doc(mapId);
//         docRef.get().then(async (doc) => {
//             if (doc.exists) {
//                 console.log("Document data:", doc.data());
//                 let newMap = doc.data();
//                 let firebaseDev = admin.initializeApp({
//                     credential: admin.credential.cert(serviceAccountProd)
//                 }, "dev");
//                 const newdocRef = firebaseDev.firestore().collection('maps').doc(mapId);
//
//                 await newdocRef.create(newMap);
//
//             } else {
//                 console.log("No such document!");
//             }
//         }).catch((error) => {
//             console.log("Error getting document:", error);
//         });
//
//         // await docRef.update(newMapData);
//         // await docRef.create(docRef.collection().doc);
//
//         // console.log(`Document ${mapId} updated.`);
//
//         // console.log('All documents updated successfully!');
//     } catch (error) {
//         console.error('Error:', error);
//     }
// })();

const mapId = "lobby_2";
const mapId2 = "lobby_3";

(async () => {
    try {
        const docRef = db.collection('maps').doc(mapId);
        docRef.get().then(async (doc) => {
            if (doc.exists) {
                console.log("Document data:", doc.data());
                let newMap = doc.data();
                newMap.name = "Lobby 3";
                const newdocRef = db.collection('maps').doc(mapId2);

                await newdocRef.create(newMap);

            } else {
                console.log("No such document!");
            }
        }).catch((error) => {
            console.log("Error getting document:", error);
        });
    } catch (error) {
        console.error('Error:', error);
    }
})();