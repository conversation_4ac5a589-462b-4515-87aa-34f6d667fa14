const { CurrenciesApi } = require("@unity-services/economy-2.5");
const { DataApi } = require("@unity-services/cloud-save-1.4");

module.exports = async ({ params, context, logger }) => {
    const { projectId, environmentId } = context;
    const { oculusUsernames, amount } = params;

    const currenciesApi = new CurrenciesApi(context);
    const cloudSaveApi = new DataApi(context);
    const balanceChanges = [];

    try {
        // Split the usernames into an array
        const usernames = oculusUsernames.includes(",") ? oculusUsernames.split(",") : [oculusUsernames];
        logger.info(`usernames: ${usernames}`);

        // Retrieve Unity Player IDs and add changes
        for (const username of usernames) {
            const query = {
                fields: [{
                    asc: true,
                    key: 'oculusUsername',
                    op: 'EQ',
                    value: username.trim(),
                }]
            };

            const queryRes = await cloudSaveApi.queryDefaultPlayerData(projectId, query);
            const results = queryRes.data.results;

            if (results.length > 0) {
                const unityPlayerId = results[0].id;

                if (amount !== 0) {
                    const newBalance = await adjustCurrencyBalance(unityPlayerId, "REMZ", amount, projectId, currenciesApi, logger);
                    balanceChanges.push({ username, playerId: unityPlayerId, change: amount, newBalance });
                } else {
                    logger.info(`No balance change specified for player ${username}. Skipping.`);
                }
            } else {
                logger.info(`No Unity player ID found for username: ${username}`);
            }
        }

        return {
            message: `Successfully updated balances for ${balanceChanges.length} players.`,
            details: balanceChanges
        };
    } catch (err) {
        logger.error("Error while processing usernames", { "error.message": err.message });
        throw err;
    }
};

async function adjustCurrencyBalance(playerId, currencyKey, amount, projectId, currenciesApi, logger) {
    try {
        const adjustRequest = {
            amount: Math.abs(amount)
        };

        if (amount < 0) {
            const result = await currenciesApi.decrementPlayerCurrencyBalance({
                projectId: projectId,
                playerId: playerId,
                currencyId: currencyKey,
                currencyModifyBalanceRequest: adjustRequest,
            });
            logger.info(
                `Currency ${currencyKey} decremented by ${Math.abs(amount)} for player ${playerId}. New balance: ${result.data.balance}`
            );
            return result.data.balance;
        } else {
            const result = await currenciesApi.incrementPlayerCurrencyBalance({
                projectId: projectId,
                playerId: playerId,
                currencyId: currencyKey,
                currencyModifyBalanceRequest: adjustRequest,
            });
            logger.info(
                `Currency ${currencyKey} incremented by ${amount} for player ${playerId}. New balance: ${result.data.balance}`
            );
            return result.data.balance;
        }
    } catch (err) {
        logger.error(
            `Error while adjusting currency for player ID: ${playerId}`,
            { "error.message": err.message }
        );
        throw err;
    }
}