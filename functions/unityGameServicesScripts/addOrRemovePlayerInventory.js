/*
oculusUsernames is in the format "dirkazoid,some<PERSON>ne<PERSON><PERSON><PERSON>,<PERSON>"

inventoryKey is all caps, for example: POLLY_INV, or KNIFE_INV. 
It is the inventory item in UGS economy you want to add or remove for a list of players.
*/


const _ = require("lodash-4.17");
const {DataApi} = require("@unity-services/cloud-save-1.4");
const {InventoryApi} = require("@unity-services/economy-2.4");

module.exports = async ({params, context, logger}) => {
    const {projectId, environmentId} = context;
    const {oculusUsernames, inventoryKey, add} = params;
    const cloudSaveApi = new DataApi(context);
    const inventoryApi = new InventoryApi(context);

    try {
        const usernames = oculusUsernames.includes(",") ? oculusUsernames.split(",") : [oculusUsernames];
        const unityPlayerIds = [];

        for (const username of usernames) {
            const query = {
                fields: [{
                    asc: true,
                    key: 'oculusUsername',
                    op: 'EQ',
                    value: username.trim(),
                }]
            };
            const queryRes = await cloudSaveApi.queryDefaultPlayerData(projectId, query);
            const results = queryRes.data.results;
            logger.info(`Query results for username ${username}: ${JSON.stringify(results)}`);

            if (results.length > 0) {
                const unityPlayerId = results[0].id;
                unityPlayerIds.push(unityPlayerId);
            }
        }

        logger.info(`${unityPlayerIds.length} ${JSON.stringify(unityPlayerIds)}`)

        for (const unityPlayerId of unityPlayerIds) {
            await handleInventoryAction(unityPlayerId, inventoryKey, add, projectId, inventoryApi, logger);
        }
    } catch (err) {
        logger.error("Error while processing usernames", {"error.message": err.message});
        throw err;
    }
};

async function handleInventoryAction(unityPlayerId, inventoryKey, add, projectId, inventoryApi, logger) {
    try {
        if (add) {
            const addRequest = {
                playerId: unityPlayerId,
                projectId: projectId,
                addInventoryRequest:
                    {
                        inventoryItemId: inventoryKey
                    }
            };
            await inventoryApi.addInventoryItem(addRequest);
            logger.info(`Item ${inventoryKey} added to player ${unityPlayerId}'s inventory.`);
        } else {
            logger.info(`Now fetching Player inventory for player ${unityPlayerId}`)
            const playerInventory = await inventoryApi.getPlayerInventory({
                playerId: unityPlayerId,
                projectId: projectId,
                limit: 100
            });

            const inventoryItems = playerInventory.data.results;
            logger.info(`Player inventory: ${JSON.stringify(inventoryItems)}`)

            const itemToRemove = inventoryItems.find(item => item.inventoryItemId === inventoryKey);
            logger.info(`Item to remove: ${JSON.stringify(itemToRemove)}`)

            if (itemToRemove) {
                const deleteRequest = {
                    playerId: unityPlayerId,
                    projectId: projectId,
                    playersInventoryItemId: itemToRemove.playersInventoryItemId,
                    inventoryDeleteRequest: {}
                };
                logger.info(`Attempting to remove item ${inventoryKey} from player ${unityPlayerId}`);
                await inventoryApi.deleteInventoryItem(deleteRequest);
                logger.info(`Item ${inventoryKey} removed from player ${unityPlayerId}'s inventory.`);
            } else {
                logger.info(`Item ${inventoryKey} not found in player ${unityPlayerId}'s inventory.`);
            }
        }
    } catch (err) {
        logger.error(`Error while handling inventory action for Unity player ID: ${unityPlayerId}`, {"error.message": err.message});
        throw err;
    }
}

