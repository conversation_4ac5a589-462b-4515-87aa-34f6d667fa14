/*
Use this spreadsheet to copy the items you want to add: 
https://docs.google.com/spreadsheets/d/1cwO9iMRAcZTnJ-cOWut8hVY52FvZN3jbviu5m_bkMko


copy all the rows and past it into "items" on the right. 
*/


const _ = require("lodash-4.17");
const axios = require('axios');

const SERVICE_ACCOUNT_ID = '083b1d77-c6b3-4011-bd18-b427876afeba';
const SERVICE_ACCOUNT_SECRET = 'NtEGk01dcUvWkJLueJsblUXqj8d_xNzh';

const credentials = `${SERVICE_ACCOUNT_ID}:${SERVICE_ACCOUNT_SECRET}`;
const base64Credentials = Buffer.from(credentials).toString('base64');

module.exports = async ({params, context, logger}) => {
    logger.info("Script parameters: " + JSON.stringify(params));
    logger.info("Authenticated within the following context: " + JSON.stringify(context));

    // Get projectId and environmentId from context
    const {projectId, environmentId} = context;

    const inventoryItems = createInventoryItems(params.items); // Assuming items are passed in params

    for (const item of inventoryItems) {
        try {
            // Log each item before posting it
            logger.info(`Attempting to post item: ${JSON.stringify(item)}`);
            await postInventoryItem(item, projectId, environmentId, logger, JSON);
        } catch (error) {
            logger.error(`Failed to post item ${item.id}: ${error.message}. Continuing with the next item.`);
            // Continue to the next item without throwing an error
        }
    }

    logger.info('All items processed.');
    return {status: 'success', message: 'Items processed (with potential failures)'};
};

// Function to create inventory items by splitting on '|' and ','
function createInventoryItems(data) {
    const itemsData = data.split('|');

    const inventoryItems = itemsData.filter(item => item.trim().length > 0).map(item => {
        const [name, visibleName, description, amount, category, subcategory, unlockConditions, isHidden] = item.split('/');

        const customData = {};
        if (category) customData.category = category.trim();
        if (subcategory) customData.subcategory = subcategory.trim();
        if (unlockConditions) customData.unlockConditions = unlockConditions.trim();
        if (description) customData.description = description.trim();

        return {
            id: name.toUpperCase().trim() + "_INV",
            name: visibleName.trim() + " Inv",
            type: "INVENTORY_ITEM",
            ...(Object.keys(customData).length > 0 && {customData})
        };
    });

    return inventoryItems;
}

async function postInventoryItem(newItem, projectId, environmentId, logger, JSON) {
    const headers = {
        'Authorization': `Basic ${base64Credentials}`,
        'Content-Type': 'application/json',
    };

    logger.info(JSON.stringify(headers));
    logger.info(JSON.stringify(newItem));

    const url = `https://services.api.unity.com/economy/v2/projects/${projectId}/environments/${environmentId}/configs/draft/resources`;

    try {
        const response = await axios.post(url, newItem, {headers});
        logger.info(`Item added: ${response.data}`);
        return response.data;
    } catch (error) {
        // Log full error response for troubleshooting
        logger.error(`Failed to post item ${newItem.id}: ${error.message}`);
        if (error.response) {
            logger.error(`Error response: ${JSON.stringify(error.response.data)}`);
        }
        throw error;
    }
}
