/*
Use this script to add or remove labels for a player. 
Specific usecase is to make a user a moderator or moderator trainee

Available User Labels (always check the code for most up to date labels): 
None = 0,
Tester = 1,
Moderator = 2,
TraineeMod = 3,
ContentCreator1 = 4,
ContentCreator2 = 5,
ContentCreator3 = 6
*/



const _ = require("lodash");
const { DataApi } = require("@unity-services/cloud-save-1.4");

module.exports = async ({ params, context, logger }) => {
    const { projectId } = context;
    const { oculusUsername, label, add } = params;
    const cloudSaveApi = new DataApi(context);

    try {
        const playerId = await findPlayerIdByOculusUsername(cloudSaveApi, projectId, oculusUsername, logger);
        if (!playerId) {
            return { success: false, message: `Player not found for Oculus username: ${oculusUsername}` };
        }

        let currentLabels = await fetchExistingLabels(cloudSaveApi, projectId, playerId, logger);
        let updatedLabels = updateLabels(currentLabels, label, add, logger);

        if (!updatedLabels) {
            logger.info("No changes necessary, labels remain unchanged.");
            return { success: true, labels: currentLabels };
        }

        await saveUpdatedLabels(cloudSaveApi, projectId, playerId, updatedLabels, logger);
        return { success: true, labels: updatedLabels };

    } catch (err) {
        logger.error("Error while processing labels", { "error.message": err.message });
        return { success: false, message: `An error occurred: ${err.message}` };
    }
};

// Function to find the player ID by Oculus username
async function findPlayerIdByOculusUsername(cloudSaveApi, projectId, oculusUsername, logger) {
    const query = {
        fields: [{
            asc: true,
            key: 'oculusUsername',
            op: 'EQ',
            value: oculusUsername,
        }]
    };

    logger.info(`Query params: ${JSON.stringify(query)}`);
    const queryRes = await cloudSaveApi.queryDefaultPlayerData(projectId, query);
    const results = queryRes.data.results;

    if (results.length > 0) {
        logger.info(`Found player ID: ${results[0].id}`);
        return results[0].id;
    } else {
        logger.info(`No player found for Oculus username: ${oculusUsername}`);
        return null;
    }
}

// Function to fetch existing labels from Cloud Save
async function fetchExistingLabels(cloudSaveApi, projectId, playerId, logger) {
    const labelsKey = "labels";
    let currentLabels = [];

    try {
        const getItemsRes = await cloudSaveApi.getItems(projectId, playerId, [labelsKey]);
        const item = getItemsRes.data.results.find(item => item.key === labelsKey);
        currentLabels = item ? item.value : [];
        logger.info(`Fetched current labels: ${JSON.stringify(currentLabels)}`);
    } catch (err) {
        if (err.response && err.response.status === 404) {
            logger.info(`No existing labels found for player ${playerId}. Initializing to empty array.`);
            currentLabels = [];
        } else {
            throw err;
        }
    }

    return currentLabels;
}

// Function to update the labels based on the add/remove action
function updateLabels(currentLabels, label, add, logger) {
    if (add) {
        if (!currentLabels.includes(label)) {
            logger.info(`Adding label: ${label}`);
            return [...currentLabels, label];
        } else {
            logger.info(`Label ${label} already exists. No changes made.`);
            return null; // No change needed
        }
    } else {
        if (currentLabels.includes(label)) {
            logger.info(`Removing label: ${label}`);
            return currentLabels.filter(existingLabel => existingLabel !== label);
        } else {
            logger.info(`Label ${label} does not exist. No changes made.`);
            return null; // No change needed
        }
    }
}

// Function to save the updated labels to Cloud Save
async function saveUpdatedLabels(cloudSaveApi, projectId, playerId, updatedLabels, logger) {
    const labelsKey = "labels";
    await cloudSaveApi.setItem(projectId, playerId, {
        key: labelsKey,
        value: updatedLabels
    });
    logger.info(`Successfully updated labels for player ${playerId}: ${JSON.stringify(updatedLabels)}`);
}
