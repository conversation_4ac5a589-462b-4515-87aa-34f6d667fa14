/*
Use this script to find the unity player ID from the oculusUsername. 
*/


const _ = require("lodash-4.17");
const {DataApi} = require("@unity-services/cloud-save-1.4");


module.exports = async ({params, context, logger}) => {
    const {projectId} = context;
    const {oculusUsername} = params;
    const cloudSaveApi = new DataApi(context);

    try {
        const query = {
            fields: [{
                asc: true,
                key: 'oculusUsername',
                op: 'EQ',
                value: oculusUsername,
            }]
        };
        logger.info(`Query params: ${JSON.stringify(query)}`)

        const queryRes = await cloudSaveApi.queryDefaultPlayerData(projectId, query);
        const results = queryRes.data.results;

        logger.info(`Query results: ${JSON.stringify(results)}`)
        if (results.length > 0) {
            return results[0].id;
        }
        return "";
    } catch (err) {
        logger.error("Error while calling out to Cloud Save", {"error.message": err.message});
        throw err;
    }

};