/* 
Use this script to set a player property in Cloud Save
*/

const _ = require("lodash");
const { DataApi } = require("@unity-services/cloud-save-1.4");

module.exports = async ({ params, context, logger }) => {
    const { projectId } = context;
    const { playerId, key, value } = params;
    const cloudSaveApi = new DataApi(context);

    let parsedValue;

    try {
        // Try to parse the value as JSON (for objects/arrays)
        parsedValue = JSON.parse(value);
    } catch (e) {
        // If it fails, just treat it as a raw value (string, number, etc.)
        parsedValue = value;
    }

    try {
        // Set the item in Cloud Save
        await cloudSaveApi.setItem(projectId, playerId, {
            key: key,
            value: parsedValue
        });

        logger.info(`Successfully set the value for key ${key} for player ${playerId}`);
    } catch (err) {
        logger.error("Error while calling out to Cloud Save", { "error.message": err.message });
        throw err;
    }
};
