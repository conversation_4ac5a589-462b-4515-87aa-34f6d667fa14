module.exports = {
    env: {
        es6: true,
        node: true,
    },
    parserOptions: {
        ecmaVersion: 2018,
    },
    extends: [
        "eslint:recommended",
        "google",
    ],
    rules: {
        "quote-props": "off",
        "comma-dangle": "off",
        "arrow-parens": "off",
        "eol-last": "off",
        "semi": "off",
        "no-trailing-spaces": "off",
        "no-unused-vars": "off",
        "max-len": ["off"], // Turn off line length checks
        "valid-jsdoc": "off",
        "object-curly-spacing": "off",
        "indent": "off", // Disable indentation checks
        "quotes": "off", // Allow mixed quote usage
    },
};
