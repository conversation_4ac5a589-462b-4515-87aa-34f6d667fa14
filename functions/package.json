{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"lint": "eslint .", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "deploy:dev": "NODE_ENV=development firebase deploy --only functions --project beastcraft-dev", "deploy:prod": "NODE_ENV=production firebase deploy --only functions --project beastcraft-prod"}, "engines": {"node": "18"}, "main": "index.js", "dependencies": {"busboy": "^1.6.0", "dotenv": "^16.4.5", "firebase-admin": "^12.6.0", "firebase-functions": "^6.0.1", "jsonwebtoken": "^9.0.2", "node-fetch": "^2.7.0", "uuid": "^11.0.3"}, "private": true}