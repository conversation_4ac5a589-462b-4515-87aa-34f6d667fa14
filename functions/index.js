const {onRequest} = require('firebase-functions/v2/https');
const admin = require('firebase-admin');
const {withAuthentication} = require('./src/auth');

admin.initializeApp();

// Functions
const createLevel = require('./src/createLevel');
const getLevelList = require('./src/getLevelList');
const getPromoLevelList = require('./src/getPromoLevelList');
const getLobbyLevelList = require('./src/getLobbyLevelList');
const getLevelData = require('./src/getLevelData');
const saveLevelData = require('./src/saveLevelData');
const getMapDownloadUrl = require('./src/getMapDownloadUrl');
const getMapUploadUrl = require('./src/getMapUploadUrl');

const generateMapDownloadURL = require('./src/generateMapDownloadURL');
const generateMapSaveUploadURL = require('./src/generateMapSaveUploadURL');
const generateMapRuntimeUploadURL = require('./src/generateMapRuntimeUploadURL');

const getToken = require('./src/getToken');
const {checkProfanityHandler} = require("./src/checkProfanity");
const getAppVersion = require('./src/getAppVersion');
const setAppVersion = require('./src/setAppVersion');

exports.createLevel = onRequest(withAuthentication(createLevel));
exports.getLevelList = onRequest(withAuthentication(getLevelList));
exports.getPromoLevelList = onRequest(withAuthentication(getPromoLevelList));
exports.getLobbyLevelList = onRequest(withAuthentication(getLobbyLevelList));
exports.getLevelData = onRequest(withAuthentication(getLevelData));
exports.saveLevelData = onRequest(withAuthentication(saveLevelData));
exports.getMapDownloadUrl = onRequest(withAuthentication(getMapDownloadUrl));
exports.getMapUploadUrl = onRequest(withAuthentication(getMapUploadUrl));

exports.generateMapDownloadURL = onRequest(withAuthentication(generateMapDownloadURL));
exports.generateMapSaveUploadURL = onRequest(withAuthentication(generateMapSaveUploadURL));
exports.generateMapRuntimeUploadURL = onRequest(withAuthentication(generateMapRuntimeUploadURL));

exports.getToken = onRequest(getToken);
exports.checkProfanity = onRequest(checkProfanityHandler);
exports.getAppVersion = onRequest(getAppVersion);
exports.setAppVersion = onRequest(setAppVersion);
