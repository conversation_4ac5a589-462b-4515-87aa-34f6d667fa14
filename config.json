[{"type": "INVENTORY_ITEM", "id": "ALBATROSS_BODY", "name": "Albatros Body", "customData": {"description": "Albatros body"}, "created": {"date": "2023-08-29T19:20:10Z"}, "modified": {"date": "2023-08-29T19:20:10Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "ALBATROSS_BODY_PURCHASE", "name": "Albatros Body Purchase", "costs": [], "rewards": [{"resourceId": "ALBATROSS_BODY", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:11Z"}, "modified": {"date": "2023-08-29T19:20:11Z"}}, {"type": "INVENTORY_ITEM", "id": "ALBATROSS_HEAD", "name": "Albatros Head", "customData": {"description": "Albatros head"}, "created": {"date": "2023-08-29T19:20:13Z"}, "modified": {"date": "2023-08-29T19:20:13Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "ALBATROSS_HEAD_PURCHASE", "name": "Albatros Head Purchase", "costs": [], "rewards": [{"resourceId": "ALBATROSS_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:13Z"}, "modified": {"date": "2023-08-29T19:20:13Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "ALBATROSS_SET_PURCHASE", "name": "Albatros Purchase", "costs": [], "rewards": [{"resourceId": "ALBATROSS_WINGS", "amount": 1, "defaultInstanceData": null}, {"resourceId": "ALBATROSS_BODY", "amount": 1, "defaultInstanceData": null}, {"resourceId": "ALBATROSS_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:22:16Z"}, "modified": {"date": "2023-08-29T19:22:16Z"}}, {"type": "INVENTORY_ITEM", "id": "ALBATROSS_WINGS", "name": "Albatros Wings", "customData": {"description": "Albatros wings"}, "created": {"date": "2023-08-29T19:20:09Z"}, "modified": {"date": "2023-08-29T19:20:09Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "ALBATROSS_WINGS_PURCHASE", "name": "Albatros Wings Purchase", "costs": [], "rewards": [{"resourceId": "ALBATROSS_WINGS", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:09Z"}, "modified": {"date": "2023-08-29T19:20:09Z"}}, {"type": "INVENTORY_ITEM", "id": "BAT_BODY", "name": "Bat Body", "customData": {"description": "Bat body"}, "created": {"date": "2023-08-29T19:20:14Z"}, "modified": {"date": "2023-08-29T19:20:14Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "BAT_BODY_PURCHASE", "name": "Bat Body Purchase", "costs": [{"resourceId": "REMZ", "amount": 4000}], "rewards": [{"resourceId": "BAT_BODY", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:14Z"}, "modified": {"date": "2023-08-29T19:20:14Z"}}, {"type": "INVENTORY_ITEM", "id": "BAT_HEAD", "name": "<PERSON> Head", "customData": {"description": "Bat head"}, "created": {"date": "2023-08-29T19:20:22Z"}, "modified": {"date": "2023-08-29T19:20:22Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "BAT_HEAD_PURCHASE", "name": "Bat Head Purchase", "costs": [{"resourceId": "REMZ", "amount": 4000}], "rewards": [{"resourceId": "BAT_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:23Z"}, "modified": {"date": "2023-08-29T19:20:23Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "BAT_SET_PURCHASE", "name": "Bat Purchase", "costs": [{"resourceId": "REMZ", "amount": 10000}], "rewards": [{"resourceId": "BAT_WINGS", "amount": 1, "defaultInstanceData": null}, {"resourceId": "BAT_BODY", "amount": 1, "defaultInstanceData": null}, {"resourceId": "BAT_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:22:13Z"}, "modified": {"date": "2023-08-29T19:22:13Z"}}, {"type": "INVENTORY_ITEM", "id": "BAT_WINGS", "name": "Bat Wings", "customData": {"description": "Bat wings"}, "created": {"date": "2023-08-29T19:20:27Z"}, "modified": {"date": "2023-08-29T19:20:27Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "BAT_WINGS_PURCHASE", "name": "Bat Wings Purchase", "costs": [{"resourceId": "REMZ", "amount": 4000}], "rewards": [{"resourceId": "BAT_WINGS", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:28Z"}, "modified": {"date": "2023-08-29T19:20:28Z"}}, {"type": "INVENTORY_ITEM", "id": "BLACKHERON_BODY", "name": "Blackheron'S Body", "customData": {"description": "<PERSON><PERSON><PERSON>'s body"}, "created": {"date": "2023-08-29T19:20:15Z"}, "modified": {"date": "2023-08-29T19:20:15Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "BLACKHERON_BODY_PURCHASE", "name": "Blackheron'S Body Purchase", "costs": [{"resourceId": "REMZ", "amount": 1000}], "rewards": [{"resourceId": "BLACKHERON_BODY", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:15Z"}, "modified": {"date": "2023-08-29T19:20:15Z"}}, {"type": "INVENTORY_ITEM", "id": "BLACKHERON_HEAD", "name": "Blackheron'S Head", "customData": {"description": "<PERSON><PERSON><PERSON>'s head"}, "created": {"date": "2023-08-29T19:20:14Z"}, "modified": {"date": "2023-08-29T19:20:14Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "BLACKHERON_HEAD_PURCHASE", "name": "Blackheron'S Head Purchase", "costs": [{"resourceId": "REMZ", "amount": 1000}], "rewards": [{"resourceId": "BLACKHERON_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:14Z"}, "modified": {"date": "2023-08-29T19:20:14Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "BLACKHERON_SET_PURCHASE", "name": "Blackheron Purchase", "costs": [{"resourceId": "REMZ", "amount": 2500}], "rewards": [{"resourceId": "BLACKHERON_WINGS", "amount": 1, "defaultInstanceData": null}, {"resourceId": "BLACKHERON_BODY", "amount": 1, "defaultInstanceData": null}, {"resourceId": "BLACKHERON_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:22:20Z"}, "modified": {"date": "2023-08-29T19:22:20Z"}}, {"type": "INVENTORY_ITEM", "id": "BLACKHERON_WINGS", "name": "Blackheron'S Wings", "customData": {"description": "<PERSON><PERSON><PERSON>'s wings"}, "created": {"date": "2023-08-29T19:20:29Z"}, "modified": {"date": "2023-08-29T19:20:29Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "BLACKHERON_WINGS_PURCHASE", "name": "Blackheron'S Wings Purchase", "costs": [{"resourceId": "REMZ", "amount": 1000}], "rewards": [{"resourceId": "BLACKHERON_WINGS", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:30Z"}, "modified": {"date": "2023-08-29T19:20:30Z"}}, {"type": "INVENTORY_ITEM", "id": "CHICKEN_BODY", "name": "Chicken'S Body", "customData": {"description": "<PERSON>'s body"}, "created": {"date": "2023-08-29T19:20:28Z"}, "modified": {"date": "2023-08-29T19:20:28Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "CHICKEN_BODY_PURCHASE", "name": "Chicken'S Body Purchase", "costs": [{"resourceId": "REMZ", "amount": 3000}], "rewards": [{"resourceId": "CHICKEN_BODY", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:28Z"}, "modified": {"date": "2023-08-29T19:20:28Z"}}, {"type": "INVENTORY_ITEM", "id": "CHICKEN_HEAD", "name": "Chicken'S Head", "customData": {"description": "Chicken's head"}, "created": {"date": "2023-08-29T19:20:19Z"}, "modified": {"date": "2023-08-29T19:20:19Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "CHICKEN_HEAD_PURCHASE", "name": "Chicken'S Head Purchase", "costs": [{"resourceId": "REMZ", "amount": 3000}], "rewards": [{"resourceId": "CHICKEN_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:19Z"}, "modified": {"date": "2023-08-29T19:20:19Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "CHICKEN_SET_PURCHASE", "name": "Chicken Purchase", "costs": [{"resourceId": "REMZ", "amount": 8000}], "rewards": [{"resourceId": "CHICKEN_WINGS", "amount": 1, "defaultInstanceData": null}, {"resourceId": "CHICKEN_BODY", "amount": 1, "defaultInstanceData": null}, {"resourceId": "CHICKEN_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:22:11Z"}, "modified": {"date": "2023-08-29T19:22:11Z"}}, {"type": "INVENTORY_ITEM", "id": "CHICKEN_WINGS", "name": "Chicken'S Wings", "customData": {"description": "Chicken's wings"}, "created": {"date": "2023-08-29T19:20:25Z"}, "modified": {"date": "2023-08-29T19:20:25Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "CHICKEN_WINGS_PURCHASE", "name": "Chicken'S Wings Purchase", "costs": [{"resourceId": "REMZ", "amount": 3000}], "rewards": [{"resourceId": "CHICKEN_WINGS", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:25Z"}, "modified": {"date": "2023-08-29T19:20:25Z"}}, {"type": "INVENTORY_ITEM", "id": "CROW_BODY", "name": "Crow Body", "customData": {"description": "Crow body"}, "created": {"date": "2023-08-29T19:20:06Z"}, "modified": {"date": "2023-08-29T19:20:06Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "CROW_BODY_PURCHASE", "name": "Crow Body Purchase", "costs": [{"resourceId": "REMZ", "amount": 1000}], "rewards": [{"resourceId": "CROW_BODY", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:06Z"}, "modified": {"date": "2023-08-29T19:20:06Z"}}, {"type": "INVENTORY_ITEM", "id": "CROW_HEAD", "name": "Crow Head", "customData": {"description": "Crow head"}, "created": {"date": "2023-08-29T19:20:10Z"}, "modified": {"date": "2023-08-29T19:20:10Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "CROW_HEAD_PURCHASE", "name": "Crow Head Purchase", "costs": [{"resourceId": "REMZ", "amount": 1000}], "rewards": [{"resourceId": "CROW_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:10Z"}, "modified": {"date": "2023-08-29T19:20:10Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "CROW_SET_PURCHASE", "name": "Crow Purchase", "costs": [{"resourceId": "REMZ", "amount": 2500}], "rewards": [{"resourceId": "CROW_WINGS", "amount": 1, "defaultInstanceData": null}, {"resourceId": "CROW_BODY", "amount": 1, "defaultInstanceData": null}, {"resourceId": "CROW_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:22:15Z"}, "modified": {"date": "2023-08-29T19:22:15Z"}}, {"type": "INVENTORY_ITEM", "id": "CROW_WINGS", "name": "Crow Wings", "customData": {"description": "Crow wings"}, "created": {"date": "2023-08-29T19:20:16Z"}, "modified": {"date": "2023-08-29T19:20:16Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "CROW_WINGS_PURCHASE", "name": "Crow Wings Purchase", "costs": [{"resourceId": "REMZ", "amount": 1000}], "rewards": [{"resourceId": "CROW_WINGS", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:17Z"}, "modified": {"date": "2023-08-29T19:20:17Z"}}, {"type": "INVENTORY_ITEM", "id": "DRAGON_BODY", "name": "Dragon Body", "customData": {"description": "Dragon body"}, "created": {"date": "2023-08-29T19:20:18Z"}, "modified": {"date": "2023-08-29T19:20:18Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "DRAGON_BODY_PURCHASE", "name": "Dragon Body Purchase", "costs": [{"resourceId": "REMZ", "amount": 4000}], "rewards": [{"resourceId": "DRAGON_BODY", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:18Z"}, "modified": {"date": "2023-08-29T19:20:18Z"}}, {"type": "INVENTORY_ITEM", "id": "DRAGON_HEAD", "name": "Dragon Head", "customData": {"description": "Dragon head"}, "created": {"date": "2023-08-29T19:20:24Z"}, "modified": {"date": "2023-08-29T19:20:24Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "DRAGON_HEAD_PURCHASE", "name": "Dragon Head Purchase", "costs": [{"resourceId": "REMZ", "amount": 4000}], "rewards": [{"resourceId": "DRAGON_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:24Z"}, "modified": {"date": "2023-08-29T19:20:24Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "DRAGON_SET_PURCHASE", "name": "Dragon Purchase", "costs": [{"resourceId": "REMZ", "amount": 10000}], "rewards": [{"resourceId": "DRAGON_WINGS", "amount": 1, "defaultInstanceData": null}, {"resourceId": "DRAGON_BODY", "amount": 1, "defaultInstanceData": null}, {"resourceId": "DRAGON_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:22:19Z"}, "modified": {"date": "2023-08-29T19:22:19Z"}}, {"type": "INVENTORY_ITEM", "id": "DRAGON_WINGS", "name": "Dragon Wings", "customData": {"description": "Dragon wings"}, "created": {"date": "2023-08-29T19:20:15Z"}, "modified": {"date": "2023-08-29T19:20:15Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "DRAGON_WINGS_PURCHASE", "name": "Dragon Wings Purchase", "costs": [{"resourceId": "REMZ", "amount": 4000}], "rewards": [{"resourceId": "DRAGON_WINGS", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:16Z"}, "modified": {"date": "2023-08-29T19:20:16Z"}}, {"type": "INVENTORY_ITEM", "id": "HAUNTEDDOLL", "name": "Haunted Doll", "customData": {"category": "yoshi-bundle-items", "itemType": 6, "subcategory": "avatar", "unlockConditions": "Get the Haunted Doll after buying the Horror Bundle!"}, "created": {"date": "2024-08-15T11:17:38Z"}, "modified": {"date": "2024-08-15T11:17:38Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "HORRORBUNDLE", "name": "Horror Bundle", "costs": [], "rewards": [{"resourceId": "KNIFE", "amount": 1, "defaultInstanceData": null}, {"resourceId": "HAUNTEDDOLL", "amount": 1, "defaultInstanceData": null}, {"resourceId": "REMZ", "amount": 10000, "defaultInstanceData": null}], "customData": null, "created": {"date": "2024-08-15T11:22:41Z"}, "modified": {"date": "2024-08-15T11:22:41Z"}}, {"type": "INVENTORY_ITEM", "id": "HUMMINGBIRD_BODY", "name": "Hummingbird Body", "customData": {"description": "Hummingbird body"}, "created": {"date": "2023-08-29T19:20:16Z"}, "modified": {"date": "2023-08-29T19:20:16Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "HUMMINGBIRD_BODY_PURCHASE", "name": "Hummingbird Body Purchase", "costs": [{"resourceId": "REMZ", "amount": 2000}], "rewards": [{"resourceId": "HUMMINGBIRD_BODY", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:16Z"}, "modified": {"date": "2023-08-29T19:20:16Z"}}, {"type": "INVENTORY_ITEM", "id": "HUMMINGBIRD_HEAD", "name": "Hummingbird Head", "customData": {"description": "Hummingbird head"}, "created": {"date": "2023-08-29T19:20:12Z"}, "modified": {"date": "2023-08-29T19:20:12Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "HUMMINGBIRD_HEAD_PURCHASE", "name": "Hummingbird Head Purchase", "costs": [{"resourceId": "REMZ", "amount": 2000}], "rewards": [{"resourceId": "HUMMINGBIRD_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:12Z"}, "modified": {"date": "2023-08-29T19:20:12Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "HUMMINGBIRD_SET_PURCHASE", "name": "Hummingbird Purchase", "costs": [{"resourceId": "REMZ", "amount": 5000}], "rewards": [{"resourceId": "HUMMINGBIRD_WINGS", "amount": 1, "defaultInstanceData": null}, {"resourceId": "HUMMINGBIRD_BODY", "amount": 1, "defaultInstanceData": null}, {"resourceId": "HUMMINGBIRD_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:22:18Z"}, "modified": {"date": "2023-08-29T19:22:18Z"}}, {"type": "INVENTORY_ITEM", "id": "HUMMINGBIRD_WINGS", "name": "Hummingbird Wings", "customData": {"description": "Hummingbird wings"}, "created": {"date": "2023-08-29T19:20:24Z"}, "modified": {"date": "2023-08-29T19:20:24Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "HUMMINGBIRD_WINGS_PURCHASE", "name": "Hummingbird Wings Purchase", "costs": [{"resourceId": "REMZ", "amount": 2000}], "rewards": [{"resourceId": "HUMMINGBIRD_WINGS", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:24Z"}, "modified": {"date": "2023-08-29T19:20:24Z"}}, {"type": "INVENTORY_ITEM", "id": "KNIFE", "name": "Knife", "customData": {"category": "yoshi-bundle-items", "itemType": 5, "subcategory": "weapon", "unlockConditions": "Get the Knife after buying the Horror Bundle!"}, "created": {"date": "2024-08-15T11:14:59Z"}, "modified": {"date": "2024-08-15T11:14:59Z"}}, {"type": "INVENTORY_ITEM", "id": "OWL_BODY", "name": "Owl Body ", "customData": {"description": "Owl body "}, "created": {"date": "2023-08-29T19:20:25Z"}, "modified": {"date": "2023-08-29T19:20:25Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "OWL_BODY_PURCHASE", "name": "Owl Body  Purchase", "costs": [{"resourceId": "REMZ", "amount": 2500}], "rewards": [{"resourceId": "OWL_BODY", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:26Z"}, "modified": {"date": "2023-08-29T19:20:26Z"}}, {"type": "INVENTORY_ITEM", "id": "OWL_HEAD", "name": "Owl Head", "customData": {"description": "Owl head"}, "created": {"date": "2023-08-29T19:20:20Z"}, "modified": {"date": "2023-08-29T19:20:20Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "OWL_HEAD_PURCHASE", "name": "Owl Head Purchase", "costs": [{"resourceId": "REMZ", "amount": 2500}], "rewards": [{"resourceId": "OWL_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:21Z"}, "modified": {"date": "2023-08-29T19:20:21Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "OWL_SET_PURCHASE", "name": "Owl Purchase", "costs": [{"resourceId": "REMZ", "amount": 6000}], "rewards": [{"resourceId": "OWL_WINGS", "amount": 1, "defaultInstanceData": null}, {"resourceId": "OWL_BODY", "amount": 1, "defaultInstanceData": null}, {"resourceId": "OWL_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:22:17Z"}, "modified": {"date": "2023-08-29T19:22:17Z"}}, {"type": "INVENTORY_ITEM", "id": "OWL_WINGS", "name": "Owl Wings", "customData": {"description": "Owl wings"}, "created": {"date": "2023-08-29T19:20:08Z"}, "modified": {"date": "2023-08-29T19:20:08Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "OWL_WINGS_PURCHASE", "name": "Owl Wings Purchase", "costs": [{"resourceId": "REMZ", "amount": 2500}], "rewards": [{"resourceId": "OWL_WINGS", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:08Z"}, "modified": {"date": "2023-08-29T19:20:08Z"}}, {"type": "INVENTORY_ITEM", "id": "PARROT_BODY", "name": "<PERSON><PERSON><PERSON>", "customData": {"description": "Parrot body"}, "created": {"date": "2023-08-29T19:20:20Z"}, "modified": {"date": "2023-08-29T19:20:20Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "PARROT_BODY_PURCHASE", "name": "Parrot Body Purchase", "costs": [], "rewards": [{"resourceId": "PARROT_BODY", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:20Z"}, "modified": {"date": "2023-08-29T19:20:20Z"}}, {"type": "INVENTORY_ITEM", "id": "PARROT_HEAD", "name": "<PERSON><PERSON><PERSON>", "customData": {"description": "Parrot head"}, "created": {"date": "2023-08-29T19:20:30Z"}, "modified": {"date": "2023-08-29T19:20:30Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "PARROT_HEAD_PURCHASE", "name": "Parrot Head Purchase", "costs": [], "rewards": [{"resourceId": "PARROT_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:30Z"}, "modified": {"date": "2023-08-29T19:20:30Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "PARROT_SET_PURCHASE", "name": "Parrot Purchase", "costs": [], "rewards": [{"resourceId": "PARROT_WINGS", "amount": 1, "defaultInstanceData": null}, {"resourceId": "PARROT_BODY", "amount": 1, "defaultInstanceData": null}, {"resourceId": "PARROT_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:22:22Z"}, "modified": {"date": "2023-08-29T19:22:22Z"}}, {"type": "INVENTORY_ITEM", "id": "PARROT_WINGS", "name": "Parrot Wings", "customData": {"description": "Parrot wings"}, "created": {"date": "2023-08-29T19:20:05Z"}, "modified": {"date": "2023-08-29T19:20:05Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "PARROT_WINGS_PURCHASE", "name": "Parrot Wings Purchase", "costs": [], "rewards": [{"resourceId": "PARROT_WINGS", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:06Z"}, "modified": {"date": "2023-08-29T19:20:06Z"}}, {"type": "INVENTORY_ITEM", "id": "PENGUIN_BODY", "name": "Penguin'S Body", "customData": {"description": "<PERSON>'s body"}, "created": {"date": "2023-08-29T19:20:29Z"}, "modified": {"date": "2023-08-29T19:20:29Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "PENGUIN_BODY_PURCHASE", "name": "Penguin'S Body Purchase", "costs": [{"resourceId": "REMZ", "amount": 3000}], "rewards": [{"resourceId": "PENGUIN_BODY", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:29Z"}, "modified": {"date": "2023-08-29T19:20:29Z"}}, {"type": "INVENTORY_ITEM", "id": "PENGUIN_HEAD", "name": "Penguin'S Head", "customData": {"description": "<PERSON>'s head"}, "created": {"date": "2023-08-29T19:20:12Z"}, "modified": {"date": "2023-08-29T19:20:12Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "PENGUIN_HEAD_PURCHASE", "name": "Penguin'S Head Purchase", "costs": [{"resourceId": "REMZ", "amount": 3000}], "rewards": [{"resourceId": "PENGUIN_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:13Z"}, "modified": {"date": "2023-08-29T19:20:13Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "PENGUIN_SET_PURCHASE", "name": "Penguin Purchase", "costs": [{"resourceId": "REMZ", "amount": 8000}], "rewards": [{"resourceId": "PENGUIN_WINGS", "amount": 1, "defaultInstanceData": null}, {"resourceId": "PENGUIN_BODY", "amount": 1, "defaultInstanceData": null}, {"resourceId": "PENGUIN_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:22:20Z"}, "modified": {"date": "2023-08-29T19:22:20Z"}}, {"type": "INVENTORY_ITEM", "id": "PENGUIN_WINGS", "name": "Penguin'S Wings", "customData": {"description": "Penguin's wings"}, "created": {"date": "2023-08-29T19:20:22Z"}, "modified": {"date": "2023-08-29T19:20:22Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "PENGUIN_WINGS_PURCHASE", "name": "Penguin'S Wings Purchase", "costs": [{"resourceId": "REMZ", "amount": 3000}], "rewards": [{"resourceId": "PENGUIN_WINGS", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:22Z"}, "modified": {"date": "2023-08-29T19:20:22Z"}}, {"type": "INVENTORY_ITEM", "id": "PHOENIX_BODY", "name": "Phoenix'S Body", "customData": {"description": "<PERSON>'s body"}, "created": {"date": "2023-08-29T19:20:21Z"}, "modified": {"date": "2023-08-29T19:20:21Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "PHOENIX_BODY_PURCHASE", "name": "Phoenix'S Body Purchase", "costs": [{"resourceId": "REMZ", "amount": 2000}], "rewards": [{"resourceId": "PHOENIX_BODY", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:21Z"}, "modified": {"date": "2023-08-29T19:20:21Z"}}, {"type": "INVENTORY_ITEM", "id": "PHOENIX_HEAD", "name": "Phoenix'S Head", "customData": {"description": "Phoenix's head"}, "created": {"date": "2023-08-29T19:20:09Z"}, "modified": {"date": "2023-08-29T19:20:09Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "PHOENIX_HEAD_PURCHASE", "name": "Phoenix'S Head Purchase", "costs": [{"resourceId": "REMZ", "amount": 3000}], "rewards": [{"resourceId": "PHOENIX_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:09Z"}, "modified": {"date": "2023-08-29T19:20:09Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "PHOENIX_SET_PURCHASE", "name": "Phoenix Purchase", "costs": [{"resourceId": "REMZ", "amount": 7000}], "rewards": [{"resourceId": "PHOENIX_WINGS", "amount": 1, "defaultInstanceData": null}, {"resourceId": "PHOENIX_BODY", "amount": 1, "defaultInstanceData": null}, {"resourceId": "PHOENIX_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:22:14Z"}, "modified": {"date": "2023-08-29T19:22:14Z"}}, {"type": "INVENTORY_ITEM", "id": "PHOENIX_WINGS", "name": "Phoenix'S Wings", "customData": {"description": "Phoenix's wings"}, "created": {"date": "2023-08-29T19:20:04Z"}, "modified": {"date": "2023-08-29T19:20:04Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "PHOENIX_WINGS_PURCHASE", "name": "Phoenix'S Wings Purchase", "costs": [{"resourceId": "REMZ", "amount": 3000}], "rewards": [{"resourceId": "PHOENIX_WINGS", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:04Z"}, "modified": {"date": "2023-08-29T19:20:04Z"}}, {"type": "INVENTORY_ITEM", "id": "PIGEON_BODY", "name": "Pigeon Body", "customData": {"description": "Pigeon body"}, "created": {"date": "2023-08-29T19:20:11Z"}, "modified": {"date": "2023-08-29T19:20:11Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "PIGEON_BODY_PURCHASE", "name": "Pigeon Body Purchase", "costs": [], "rewards": [{"resourceId": "PIGEON_BODY", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:11Z"}, "modified": {"date": "2023-08-29T19:20:11Z"}}, {"type": "INVENTORY_ITEM", "id": "PIGEON_HEAD", "name": "Pigeon Head", "customData": {"description": "Pigeon head"}, "created": {"date": "2023-08-29T19:20:07Z"}, "modified": {"date": "2023-08-29T19:20:07Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "PIGEON_HEAD_PURCHASE", "name": "Pigeon Head Purchase", "costs": [], "rewards": [{"resourceId": "PIGEON_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:07Z"}, "modified": {"date": "2023-08-29T19:20:07Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "PIGEON_SET_PURCHASE", "name": "Pigeon Purchase", "costs": [], "rewards": [{"resourceId": "PIGEON_WINGS", "amount": 1, "defaultInstanceData": null}, {"resourceId": "PIGEON_BODY", "amount": 1, "defaultInstanceData": null}, {"resourceId": "PIGEON_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:22:12Z"}, "modified": {"date": "2023-08-29T19:22:12Z"}}, {"type": "INVENTORY_ITEM", "id": "PIGEON_WINGS", "name": "Pigeon Wings", "customData": {"description": "Pigeon wings"}, "created": {"date": "2023-08-29T19:20:07Z"}, "modified": {"date": "2023-08-29T19:20:07Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "PIGEON_WINGS_PURCHASE", "name": "Pigeon Wings Purchase", "costs": [], "rewards": [{"resourceId": "PIGEON_WINGS", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:08Z"}, "modified": {"date": "2023-08-29T19:20:08Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "PURCHASE_OPTION_10", "name": "Buy 2400 REMZ", "costs": [], "rewards": [{"resourceId": "REMZ", "amount": 2400, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T11:33:25Z"}, "modified": {"date": "2023-08-29T11:33:25Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "PURCHASE_OPTION_100", "name": "Buy 50000 REMZ", "costs": [], "rewards": [{"resourceId": "REMZ", "amount": 50000, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T11:30:47Z"}, "modified": {"date": "2023-08-29T11:30:47Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "PURCHASE_OPTION_20", "name": "Buy 7000 REMZ", "costs": [], "rewards": [{"resourceId": "REMZ", "amount": 7000, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T11:32:48Z"}, "modified": {"date": "2023-08-29T11:32:48Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "PURCHASE_OPTION_5", "name": "Buy 1000 REMZ", "costs": [], "rewards": [{"resourceId": "REMZ", "amount": 1000, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T11:34:20Z"}, "modified": {"date": "2023-08-29T11:34:20Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "PURCHASE_OPTION_50", "name": "Buy 22000 REMZ", "costs": [], "rewards": [{"resourceId": "REMZ", "amount": 22000, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T11:31:58Z"}, "modified": {"date": "2023-08-29T11:31:58Z"}}, {"type": "INVENTORY_ITEM", "id": "REMCITY_AVATARPARTS_BODYPIRATESHIRT", "name": "Pirate Shirt", "customData": {"description": "Pirate Shirt"}, "created": {"date": "2023-08-29T19:20:31Z"}, "modified": {"date": "2023-08-29T19:20:31Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "REMCITY_AVATARPARTS_BODYPIRATESHIRT_PURCHASE", "name": "Pirate Shirt Purchase", "costs": [{"resourceId": "REMZ", "amount": 1}], "rewards": [{"resourceId": "REMCITY_AVATARPARTS_BODYPIRATESHIRT", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:32Z"}, "modified": {"date": "2023-08-29T19:20:32Z"}}, {"type": "INVENTORY_ITEM", "id": "REMCITY_AVATARPARTS_BODYPOLICESHIRT", "name": "Police Shirt", "customData": {"description": "Police Shirt"}, "created": {"date": "2023-08-29T19:20:35Z"}, "modified": {"date": "2023-08-29T19:20:35Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "REMCITY_AVATARPARTS_BODYPOLICESHIRT_PURCHASE", "name": "Police Shirt Purchase", "costs": [{"resourceId": "REMZ", "amount": 2}], "rewards": [{"resourceId": "REMCITY_AVATARPARTS_BODYPOLICESHIRT", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:36Z"}, "modified": {"date": "2023-08-29T19:20:36Z"}}, {"type": "INVENTORY_ITEM", "id": "REMCITY_AVATARPARTS_BODYSAILORSHIRT", "name": "Sailor Shirt", "customData": {"description": "Sailor Shirt"}, "created": {"date": "2023-08-29T19:20:36Z"}, "modified": {"date": "2023-08-29T19:20:36Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "REMCITY_AVATARPARTS_BODYSAILORSHIRT_PURCHASE", "name": "Sailor Shirt Purchase", "costs": [{"resourceId": "REMZ", "amount": 3}], "rewards": [{"resourceId": "REMCITY_AVATARPARTS_BODYSAILORSHIRT", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:36Z"}, "modified": {"date": "2023-08-29T19:20:36Z"}}, {"type": "INVENTORY_ITEM", "id": "REMCITY_AVATARPARTS_BODYTOILETOUTFIT", "name": "Toilet Outfit", "customData": {"description": "Toilet Outfit"}, "created": {"date": "2023-08-29T19:20:33Z"}, "modified": {"date": "2023-08-29T19:20:33Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "REMCITY_AVATARPARTS_BODYTOILETOUTFIT_PURCHASE", "name": "Toilet Outfit Purchase", "costs": [{"resourceId": "REMZ", "amount": 1000}], "rewards": [{"resourceId": "REMCITY_AVATARPARTS_BODYTOILETOUTFIT", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:34Z"}, "modified": {"date": "2023-08-29T19:20:34Z"}}, {"type": "INVENTORY_ITEM", "id": "REMCITY_AVATARPARTS_BODYWAITERAPRON", "name": "Waiter <PERSON><PERSON>", "customData": {"description": "Waiter <PERSON><PERSON>"}, "created": {"date": "2023-08-29T19:20:35Z"}, "modified": {"date": "2023-08-29T19:20:35Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "REMCITY_AVATARPARTS_BODYWAITERAPRON_PURCHASE", "name": "Waiter <PERSON><PERSON>", "costs": [{"resourceId": "REMZ", "amount": 5}], "rewards": [{"resourceId": "REMCITY_AVATARPARTS_BODYWAITERAPRON", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:35Z"}, "modified": {"date": "2023-08-29T19:20:35Z"}}, {"type": "INVENTORY_ITEM", "id": "REMCITY_AVATARPARTS_GLASSES02", "name": "Glasses N1", "customData": {"description": "Glasses N1"}, "created": {"date": "2023-08-29T19:20:32Z"}, "modified": {"date": "2023-08-29T19:20:32Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "REMCITY_AVATARPARTS_GLASSES02_PURCHASE", "name": "Glasses N1 Purchase", "costs": [{"resourceId": "REMZ", "amount": 6}], "rewards": [{"resourceId": "REMCITY_AVATARPARTS_GLASSES02", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:32Z"}, "modified": {"date": "2023-08-29T19:20:32Z"}}, {"type": "INVENTORY_ITEM", "id": "REMCITY_AVATARPARTS_GLASSES03", "name": "Glasses N2", "customData": {"description": "Glasses N2"}, "created": {"date": "2023-08-29T19:20:33Z"}, "modified": {"date": "2023-08-29T19:20:33Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "REMCITY_AVATARPARTS_GLASSES03_PURCHASE", "name": "Glasses N2 Purchase", "costs": [{"resourceId": "REMZ", "amount": 7}], "rewards": [{"resourceId": "REMCITY_AVATARPARTS_GLASSES03", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:33Z"}, "modified": {"date": "2023-08-29T19:20:33Z"}}, {"type": "INVENTORY_ITEM", "id": "REMCITY_AVATARPARTS_GLASSES04", "name": "Glasses N3", "customData": {"description": "Glasses N3"}, "created": {"date": "2023-08-29T19:20:34Z"}, "modified": {"date": "2023-08-29T19:20:34Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "REMCITY_AVATARPARTS_GLASSES04_PURCHASE", "name": "Glasses N3 Purchase", "costs": [{"resourceId": "REMZ", "amount": 100000}], "rewards": [{"resourceId": "REMCITY_AVATARPARTS_GLASSES04", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:34Z"}, "modified": {"date": "2023-08-29T19:20:34Z"}}, {"type": "CURRENCY", "id": "REMZ", "name": "REMZ ", "initial": 0, "max": 0, "customData": null, "created": {"date": "2023-08-29T11:29:06Z"}, "modified": {"date": "2023-08-29T11:29:06Z"}}, {"type": "INVENTORY_ITEM", "id": "TOASTER_BODY", "name": "Toaster'S Body", "customData": {"description": "<PERSON><PERSON>'s body"}, "created": {"date": "2023-08-29T19:20:17Z"}, "modified": {"date": "2023-08-29T19:20:17Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "TOASTER_BODY_PURCHASE", "name": "Toaster'S Body Purchase", "costs": [{"resourceId": "REMZ", "amount": 4500}], "rewards": [{"resourceId": "TOASTER_BODY", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:17Z"}, "modified": {"date": "2023-08-29T19:20:17Z"}}, {"type": "INVENTORY_ITEM", "id": "TOASTER_HEAD", "name": "Toaster'S Head", "customData": {"description": "<PERSON><PERSON>'s head"}, "created": {"date": "2023-08-29T19:20:26Z"}, "modified": {"date": "2023-08-29T19:20:26Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "TOASTER_HEAD_PURCHASE", "name": "Toaster'S Head Purchase", "costs": [{"resourceId": "REMZ", "amount": 1500}], "rewards": [{"resourceId": "TOASTER_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:27Z"}, "modified": {"date": "2023-08-29T19:20:27Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "TOASTER_SET_PURCHASE", "name": "Toaster Purchase", "costs": [{"resourceId": "REMZ", "amount": 7000}], "rewards": [{"resourceId": "TOASTER_WINGS", "amount": 1, "defaultInstanceData": null}, {"resourceId": "TOASTER_BODY", "amount": 1, "defaultInstanceData": null}, {"resourceId": "TOASTER_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:22:21Z"}, "modified": {"date": "2023-08-29T19:22:21Z"}}, {"type": "INVENTORY_ITEM", "id": "TOASTER_WINGS", "name": "Toaster'S Wings", "customData": {"description": "<PERSON><PERSON>'s wings"}, "created": {"date": "2023-08-29T19:20:05Z"}, "modified": {"date": "2023-08-29T19:20:05Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "TOASTER_WINGS_PURCHASE", "name": "Toaster'S Wings Purchase", "costs": [{"resourceId": "REMZ", "amount": 1500}], "rewards": [{"resourceId": "TOASTER_WINGS", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:05Z"}, "modified": {"date": "2023-08-29T19:20:05Z"}}, {"type": "INVENTORY_ITEM", "id": "TOUCAN_BODY", "name": "Toucan Body", "customData": {"description": "Toucan body"}, "created": {"date": "2023-08-29T19:20:27Z"}, "modified": {"date": "2023-08-29T19:20:27Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "TOUCAN_BODY_PURCHASE", "name": "Toucan Body Purchase", "costs": [{"resourceId": "REMZ", "amount": 2000}], "rewards": [{"resourceId": "TOUCAN_BODY", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:27Z"}, "modified": {"date": "2023-08-29T19:20:27Z"}}, {"type": "INVENTORY_ITEM", "id": "TOUCAN_HEAD", "name": "Toucan Head", "customData": {"description": "Toucan head"}, "created": {"date": "2023-08-29T19:20:18Z"}, "modified": {"date": "2023-08-29T19:20:18Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "TOUCAN_HEAD_PURCHASE", "name": "Toucan Head Purchase", "costs": [{"resourceId": "REMZ", "amount": 3000}], "rewards": [{"resourceId": "TOUCAN_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:19Z"}, "modified": {"date": "2023-08-29T19:20:19Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "TOUCAN_SET_PURCHASE", "name": "Toucan Purchase", "costs": [{"resourceId": "REMZ", "amount": 6000}], "rewards": [{"resourceId": "TOUCAN_WINGS", "amount": 1, "defaultInstanceData": null}, {"resourceId": "TOUCAN_BODY", "amount": 1, "defaultInstanceData": null}, {"resourceId": "TOUCAN_HEAD", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:22:15Z"}, "modified": {"date": "2023-08-29T19:22:15Z"}}, {"type": "INVENTORY_ITEM", "id": "TOUCAN_WINGS", "name": "Toucan Wings", "customData": {"description": "Toucan wings"}, "created": {"date": "2023-08-29T19:20:23Z"}, "modified": {"date": "2023-08-29T19:20:23Z"}}, {"type": "VIRTUAL_PURCHASE", "id": "TOUCAN_WINGS_PURCHASE", "name": "Toucan Wings Purchase", "costs": [{"resourceId": "REMZ", "amount": 2000}], "rewards": [{"resourceId": "TOUCAN_WINGS", "amount": 1, "defaultInstanceData": null}], "customData": null, "created": {"date": "2023-08-29T19:20:23Z"}, "modified": {"date": "2023-08-29T19:20:23Z"}}]