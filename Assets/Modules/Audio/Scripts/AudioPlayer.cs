using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.Audio;

namespace Modules.Core.Audio
{
    internal class AudioPlayer : Actor
    {
        [SerializeField] private AudioSource audioSource;
        [SerializeField] private AudioMixer audioMixer;

        private AudioPlayerPool audioPlayerPool;

        private static readonly Dictionary<AudioMixerGroup, string> GroupToName = new()
        {
            { AudioMixerGroup.Sfx, "Sfx" },
            { AudioMixerGroup.Interface, "Interface" },
            { AudioMixerGroup.Music, "Music" }
        };

        public string Key { get; private set; }

        public void Initialize(string key, AudioData audioData, AudioPlayerPool audioPlayerPool)
        {
            Key = key;

            audioSource.enabled = true;
            audioSource.outputAudioMixerGroup = GetMixerGroup(audioData.group);
            audioSource.clip = audioData.clip;
            audioSource.loop = audioData.loop;
            audioSource.volume = audioData.volume;
            audioSource.spatialBlend = audioData.isSpatial ? 1 : 0;
            audioSource.minDistance = audioData.minDistance;
            audioSource.maxDistance = audioData.maxDistance;
            audioSource.rolloffMode = audioData.rolloffMode;

            this.audioPlayerPool = audioPlayerPool;
        }

        public async UniTask PlayAsync(CancellationToken cancellationToken)
        {
            var resultCancellationToken = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, destroyCancellationToken).Token;
            await using var disposable = resultCancellationToken.Register(Stop);
            await audioSource.PlayAsync(resultCancellationToken);
            Stop();
        }

        public void SetPosition(Vector3 position)
        {
            transform.position = position;
        }

        public void SetLocalPosition(Vector3 position)
        {
            transform.localPosition = position;
        }

        public void Stop()
        {
            Key = null;
            audioSource.Stop();
            audioSource.clip = null;
            audioPlayerPool.Release(this);
        }

        private UnityEngine.Audio.AudioMixerGroup GetMixerGroup(AudioMixerGroup group)
        {
            if (!GroupToName.TryGetValue(group, out var groupName))
            {
                return null;
            }

            var mixerGroup = audioMixer.FindMatchingGroups(groupName);
            return mixerGroup.Length > 0 ? mixerGroup[0] : null;
        }
    }
}