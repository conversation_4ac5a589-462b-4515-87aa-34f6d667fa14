{"name": "Modules.Network", "rootNamespace": "", "references": ["Modules.Core", "Modules.Analytics", "Fusion.Unity", "PhotonVoice.API", "PhotonVoice.Fusion", "PhotonVoice", "PhotonRealtime", "<PERSON><PERSON><PERSON>", "UniTask"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": true, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}