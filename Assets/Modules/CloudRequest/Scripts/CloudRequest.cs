using System;
using System.IO;
using System.Text;
using System.Threading;
using Best.HTTP;
using Cysharp.Threading.Tasks;
using Modules.Core;
using UnityEngine;
using UnityEngine.Networking;

namespace Modules.CloudRequest
{
    internal class CloudRequest : ICloudRequest
    {
        private readonly string appVersion;
        private readonly ISerializer defaultSerializer;
        private readonly ISerializer unitySerializer;

        private string token;

        public CloudRequest(AppConfig appConfig)
        {
            defaultSerializer = new DefaultSerializer();
            unitySerializer = new UnitySerializer();
            appVersion = appConfig.GameVersion;
        }

        public void SetToken(string token)
        {
            this.token = token;
        }

        public async UniTask<Response<T>> GetObject<T>(Uri uri, Method method, Payload payload, RequestConfig config = default)
        {
            string content;
            HTTPRequest request = null;

            try
            {
                request = CreateRequest(uri, method, payload, config);
                content = await request.GetAsStringAsync(payload.cancellationToken);
                Logger.CloudRequest.Debug("Response received. url:{0}, code:{1}, content:{2}", uri.AbsoluteUri, request.Response.StatusCode, content);
            }
            catch (OperationCanceledException e)
            {
                Logger.CloudRequest.Debug("Request canceled. url:{0}", uri.AbsoluteUri);
                return new Response<T>(Errors.CanceledError(e.Message));
            }
            catch (AsyncHTTPException e)
            {
                var state = request == null ? string.Empty : request.State.ToString();
                var message = $"HTTPException happened. url:{uri.AbsoluteUri}, code:{e.StatusCode}, state: {state}, content:{e.Content}";
                Logger.CloudRequest.Fatal(new CloudRequestException(message));
                return new Response<T>(Errors.HttpError(GetErrorCode(e.StatusCode), message), GetInfo<T>(e.Content, config));
            }
            catch (Exception e)
            {
                var message = $"Exception happened. url:{uri.AbsoluteUri}, message:{e.Message}";
                Logger.CloudRequest.Fatal(new CloudRequestException(message));
                return new Response<T>(Errors.UnknownError(message));
            }

            try
            {
                T data;
                var info = string.Empty;
                var serializer = GetSerializer(config.serializerType);

                switch (config.wrapperType)
                {
                    case WrapperType.Data:
                        var rawData = serializer.Deserialize<DataWrapper<T>>(content);
                        data = rawData.data;
                        info = rawData.message;
                        break;
                    case WrapperType.Body:
                        var rawBody = serializer.Deserialize<BodyWrapper>(content);
                        data = serializer.Deserialize<T>(rawBody.body);
                        info = rawBody.message;
                        break;
                    default:
                        data = serializer.Deserialize<T>(content);
                        break;
                }

                return new Response<T>(data, info);
            }
            catch (Exception e)
            {
                var message = $"Cannot deserialize object of type {typeof(T)}, message: {e.Message}, content: {content}";
                Logger.CloudRequest.Fatal(new CloudRequestException(message));
                return new Response<T>(Errors.DeserializationError(message));
            }
        }

        public async UniTask<Response<string>> GetString(Uri uri, Method method, Payload payload, RequestConfig config = default)
        {
            string content;
            HTTPRequest request = null;

            try
            {
                request = CreateRequest(uri, method, payload, config);
                content = await request.GetAsStringAsync(payload.cancellationToken);
                Logger.CloudRequest.Debug("Response received. url:{0}, code:{1}, content:{2}", uri.AbsoluteUri, request.Response.StatusCode, content);
            }
            catch (OperationCanceledException e)
            {
                Logger.CloudRequest.Debug("Request canceled. url:{0}", uri.AbsoluteUri);
                return new Response<string>(Errors.CanceledError(e.Message));
            }
            catch (AsyncHTTPException e)
            {
                var state = request == null ? string.Empty : request.State.ToString();
                var message = $"HTTPException happened. url:{uri.AbsoluteUri}, code:{e.StatusCode}, state: {state}, content:{e.Content}";

                if (e.StatusCode == 404)
                {
                    Logger.CloudRequest.Debug(message);
                }
                else
                {
                    Logger.CloudRequest.Fatal(new CloudRequestException(message));
                }

                return new Response<string>(Errors.HttpError(GetErrorCode(e.StatusCode), message), GetInfo<string>(e.Content, config));
            }
            catch (Exception e)
            {
                var message = $"Exception happened. url:{uri.AbsoluteUri}, message:{e.Message}";
                Logger.CloudRequest.Fatal(new CloudRequestException(message));
                return new Response<string>(Errors.UnknownError(message));
            }

            return new Response<string>(content);
        }

        public async UniTask<Response<byte[]>> GetBinary(Uri uri, Method method, Payload payload, RequestConfig config = default)
        {
            byte[] content;
            HTTPRequest request = null;

            try
            {
                request = CreateRequest(uri, method, payload, config);
                request.RetrySettings.MaxRetries = 0;
                request.DownloadSettings.ContentStreamMaxBuffered = 4 * 1024 * 1024;
                content = await request.GetRawDataAsync(payload.cancellationToken);
                Logger.CloudRequest.Debug("Response received. url:{0}, code:{1}", uri.AbsoluteUri, request.Response.StatusCode);
            }
            catch (OperationCanceledException e)
            {
                Logger.CloudRequest.Debug("Request canceled. url:{0}", uri.AbsoluteUri);
                return new Response<byte[]>(Errors.CanceledError(e.Message));
            }
            catch (AsyncHTTPException e)
            {
                var state = request == null ? string.Empty : request.State.ToString();
                var message = $"HTTPException happened. url:{uri.AbsoluteUri}, code:{e.StatusCode}, state: {state}, content:binary";
                Logger.CloudRequest.Fatal(new CloudRequestException(message));
                return new Response<byte[]>(Errors.HttpError(GetErrorCode(e.StatusCode), message), GetInfo<byte[]>(e.Content, config));
            }
            catch (Exception e)
            {
                var message = $"Exception happened. url:{uri.AbsoluteUri}, message:{e.Message}";
                Logger.CloudRequest.Fatal(new CloudRequestException(message));
                return new Response<byte[]>(Errors.UnknownError(message));
            }

            return new Response<byte[]>(content);
        }

        public async UniTask<Response<Texture>> GetTexture(Uri uri, Payload payload)
        {
            using var request = UnityWebRequestTexture.GetTexture(uri);
            Logger.CloudRequest.Debug("Request created. url:{0}, version:{1}, method:Get", uri.AbsoluteUri, appVersion);

            try
            {
                await request.SendWebRequest().WithCancellation(payload.cancellationToken);
            }
            catch (OperationCanceledException e)
            {
                Logger.CloudRequest.Debug("Request canceled. url:{0}", uri.AbsoluteUri);
                return new Response<Texture>(Errors.CanceledError(e.Message));
            }
            catch (Exception e)
            {
                var message = $"Exception happened. url:{uri.AbsoluteUri}, message:{e.Message}";
                Logger.CloudRequest.Fatal(new CloudRequestException(message));
                return new Response<Texture>(Errors.UnknownError(message));
            }

            if (request.result != UnityWebRequest.Result.Success)
            {
                var message = $"Exception happened. url:{uri.AbsoluteUri}, message:{request.error}";
                Logger.CloudRequest.Fatal(new CloudRequestException(message));
                return new Response<Texture>(Errors.UnknownError(message));
            }

            Logger.CloudRequest.Debug("Response received: code:{0}, content:texture", request.responseCode);
            return new Response<Texture>(DownloadHandlerTexture.GetContent(request));
        }

        public async UniTask<Response> SendForm(Uri uri, WWWForm form, CancellationToken cancellationToken = default)
        {
            using var request = UnityWebRequest.Post(uri, form);
            Logger.CloudRequest.Debug("Request created. url:{0}, version:{1}, method:Post", uri.AbsoluteUri, appVersion);

            try
            {
                await request.SendWebRequest().WithCancellation(cancellationToken);
            }
            catch (Exception e)
            {
                var message = $"Exception happened. url:{uri.AbsoluteUri}, message:{e.Message}";
                Logger.CloudRequest.Fatal(new CloudRequestException(message));
                return new Response(Errors.UnknownError(message));
            }

            if (request.result != UnityWebRequest.Result.Success)
            {
                var message = $"Exception happened. url:{uri.AbsoluteUri}, message:{request.error}";
                Logger.CloudRequest.Fatal(new CloudRequestException(message));
                return new Response(Errors.UnknownError(message));
            }

            Logger.CloudRequest.Debug("Response received: code:{0}, content:{1}", request.responseCode, request.downloadHandler.text);
            return new Response();
        }

        public async UniTask<Response> SendBinaryData(string url, byte[] data, CancellationToken cancellationToken = default)
        {
            using var request = UnityWebRequest.Put(url, data);
            Logger.CloudRequest.Debug("Request created. url:{0}, version:{1}, method:Post", url, appVersion);

            try
            {
                await request.SendWebRequest().WithCancellation(cancellationToken);
            }
            catch (Exception e)
            {
                var message = $"Exception happened. url:{url}, message:{e.Message}";
                Logger.CloudRequest.Fatal(new CloudRequestException(message));
                return new Response(Errors.UnknownError(message));
            }

            if (request.result != UnityWebRequest.Result.Success)
            {
                var message = $"Exception happened. url:{url}, message:{request.error}";
                Logger.CloudRequest.Fatal(new CloudRequestException(message));
                return new Response(Errors.UnknownError(message));
            }

            Logger.CloudRequest.Debug("Response received: code:{0}, content:{1}", request.responseCode, request.downloadHandler.text);
            return new Response();
        }

        private HTTPRequest CreateRequest(Uri uri, Method method, Payload payload, RequestConfig config = default)
        {
            var request = new HTTPRequest(uri, GetHttpMethod(method));
            request.TimeoutSettings.Timeout = Constants.Timeout;
            request.RetrySettings.MaxRetries = Constants.MaxRetries;
            request.SetHeader("X-Remio-Version", appVersion);

            if (!string.IsNullOrEmpty(token))
            {
                request.SetHeader("token", token);
            }

            if (payload.HasData)
            {
                byte[] data;

                if (payload.data is byte[] bytes)
                {
                    data = bytes;
                    request.SetHeader("Content-Type", "application/octet-stream");
                    Logger.CloudRequest.Debug("Request created. url:{0}, version:{1}, method:{2}, data:binary", uri.AbsoluteUri, appVersion, method);
                }
                else if (payload.data is string text)
                {
                    data = Encoding.UTF8.GetBytes(text);
                    request.SetHeader("Content-Type", "application/json");
                    Logger.CloudRequest.Debug("Request created. url:{0}, version:{1}, method:{2}, data:text", uri.AbsoluteUri, appVersion, method);
                }
                else
                {
                    var serializer = GetSerializer(config.serializerType);
                    var jsonData = serializer.Serialize(payload.data);
                    data = Encoding.UTF8.GetBytes(jsonData);
                    request.SetHeader("Content-Type", "application/json");
                    Logger.CloudRequest.Debug("Request created. url:{0}, version:{1}, method:{2}, data:{3}", uri.AbsoluteUri, appVersion, method, jsonData);
                }

                request.UploadSettings.UploadStream = new MemoryStream(data);
            }
            else
            {
                Logger.CloudRequest.Debug("Request created. url:{0}, version:{1}, method:{2}", uri.AbsoluteUri, appVersion, method);
            }

            return request;
        }

        private static HTTPMethods GetHttpMethod(Method method)
        {
            return method switch
            {
                Method.Get => HTTPMethods.Get,
                Method.Post => HTTPMethods.Post,
                Method.Put => HTTPMethods.Put,
                _ => throw new ArgumentOutOfRangeException(nameof(method), method, null)
            };
        }

        private ISerializer GetSerializer(SerializerType serializerType)
        {
            return serializerType switch
            {
                SerializerType.Unity => unitySerializer,
                _ => defaultSerializer
            };
        }

        private static ErrorCode GetErrorCode(int code)
        {
            return code switch
            {
                403 => ErrorCode.AccessDenied,
                _ => ErrorCode.HttpException
            };
        }

        private string GetInfo<T>(string content, RequestConfig config)
        {
            if (content == null)
            {
                return null;
            }

            string info = null;
            var serializer = GetSerializer(config.serializerType);

            try
            {
                switch (config.wrapperType)
                {
                    case WrapperType.Data:
                        var rawData = serializer.Deserialize<DataWrapper<T>>(content);
                        info = rawData.message;
                        break;
                    case WrapperType.Body:
                        var rawBody = serializer.Deserialize<BodyWrapper>(content);
                        info = rawBody.message;
                        break;
                }

                return info;
            }
            catch (Exception)
            {
                return null;
            }
        }
    }
}