using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace Modules.Core
{
    public interface IAudioClient
    {
        void Play(string key, Vector3 position = default, CancellationToken token = default);
        void Play(string key, Transform node, CancellationToken token = default);
        void PlayOneInstance(string key, Vector3 position = default, CancellationToken token = default);
        void PlayOneInstance(string key, Transform node, CancellationToken token = default);
        UniTask PlayAsync(string key, Vector3 position = default, bool isOneInstance = false, CancellationToken token = default);
        UniTask PlayAsync(string key, Transform node, bool isOneInstance = false, CancellationToken token = default);
        void Stop(string key);
        void StopAll();
        bool IsPlaying(string key);
        AudioClip GetClip(string key);
        void AddAudioCollection(AudioCollection audioCollection);
        void RemoveAudioCollection(AudioCollection audioCollection);
    }
}