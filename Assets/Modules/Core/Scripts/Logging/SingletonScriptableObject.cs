using UnityEngine;

namespace Modules.Core.Logging
{
    internal abstract class SingletonScriptableObject<T> : ScriptableObject where T : ScriptableObject
    {
        private static T instance;

        public static T Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = Resources.Load<T>(typeof(T).Name);
                }

                return instance;
            }
        }
    }
}