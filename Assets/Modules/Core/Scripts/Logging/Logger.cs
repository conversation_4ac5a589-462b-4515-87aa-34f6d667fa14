using System;
using System.Text;

namespace Modules.Core.Logging
{
    internal class Logger : ILogger
    {
        private static readonly StringBuilder logBuilder = new();

        public string CategoryName { get; }
        public LogLevel MinLogLevel { get; set; } = LogLevel.Verbose;
        private string Timestamp => DateTime.UtcNow.ToString("HH:mm:ss.fff");

        public Logger(string categoryName)
        {
            CategoryName = categoryName;
        }

        public void Log(LogLevel logLevel, string message, params object[] args)
        {
            lock (logBuilder)
            {
                logBuilder.Clear();
                logBuilder
                    .Append("[")
                    .Append(Timestamp)
                    .Append("] ")
                    .Append(GetLogLevelCode(logLevel))
                    .Append(" [")
                    .Append(CategoryName)
                    .Append("] ")
                    .Append(message);

                logBuilder.AppendLine();

                var output = logBuilder.ToString();
                var isArgsNull = args == null || args.Length == 0;

                switch (logLevel)
                {
                    case LogLevel.Verbose:
                        if (isArgsNull)
                        {
                            UnityEngine.Debug.Log(output);
                        }
                        else
                        {
                            UnityEngine.Debug.LogFormat(output, args);
                        }

                        break;
                    case LogLevel.Debug:
                        if (isArgsNull)
                        {
                            UnityEngine.Debug.Log(output);
                        }
                        else
                        {
                            UnityEngine.Debug.LogFormat(output, args);
                        }

                        break;
                    case LogLevel.Info:
                        if (isArgsNull)
                        {
                            UnityEngine.Debug.Log(output);
                        }
                        else
                        {
                            UnityEngine.Debug.LogFormat(output, args);
                        }

                        break;
                    case LogLevel.Warn:
                        if (isArgsNull)
                        {
                            UnityEngine.Debug.LogWarning(output);
                        }
                        else
                        {
                            UnityEngine.Debug.LogWarningFormat(output, args);
                        }

                        break;
                    case LogLevel.Error:
                        if (isArgsNull)
                        {
                            UnityEngine.Debug.LogError(output);
                        }
                        else
                        {
                            UnityEngine.Debug.LogErrorFormat(output, args);
                        }

                        break;
                    case LogLevel.Fatal:
                        if (isArgsNull)
                        {
                            UnityEngine.Debug.LogError(output);
                        }
                        else
                        {
                            UnityEngine.Debug.LogErrorFormat(output, args);
                        }

                        break;
                    case LogLevel.None:
                        break;
                    default:
                        throw new ArgumentOutOfRangeException(nameof(logLevel), logLevel, null);
                }
            }
        }

        public void LogException(Exception exception)
        {
            UnityEngine.Debug.LogException(exception);
        }

        private string GetLogLevelCode(LogLevel logLevel)
        {
            return logLevel switch
            {
                LogLevel.Verbose => "V",
                LogLevel.Debug => "D",
                LogLevel.Info => "I",
                LogLevel.Warn => "W",
                LogLevel.Error => "E",
                LogLevel.Fatal => "F",
                _ => "N"
            };
        }
    }
}