using System;
using System.Collections.Generic;
using UnityEngine;
#if UNITY_EDITOR
using Fusion.Photon.Realtime;
using UnityEditor;
#endif

namespace Modules.Core
{
    public class AppConfig : Config
    {
        [SerializeField] private ApiType apiType;
        [SerializeField] private string gameVersion;
        [SerializeField] private string firebaseBaseUri;

        [Header("Network")]
        [SerializeField] private NetworkKeys devNetworkKeys;
        [SerializeField] private NetworkKeys prodNetworkKeys;

        [Header("Admins")]
        [SerializeField] private List<AdminData> adminDataList;

        public ApiType ApiType => apiType;
        public string GameVersion => gameVersion;
        public string FirebaseBaseUri => firebaseBaseUri;
        public string UnityApiName => apiType == ApiType.Prod ? "production" : "development";
        public string GameApiName => apiType == ApiType.Prod ? "prod" : "dev";
        public bool IsDev => apiType == ApiType.Dev;
        public bool IsProd => apiType == ApiType.Prod;

        public bool IsAdmin(string oculusId)
        {
            return adminDataList.Exists(x => x.oculusId == oculusId);
        }

        public bool TryGetBuildNumber(out int buildNumber)
        {
            buildNumber = 0;

            if (string.IsNullOrEmpty(gameVersion))
            {
                return false;
            }

            var parts = gameVersion.Split('.');
            return parts.Length == 3 && int.TryParse(parts[2], out buildNumber);
        }

        public override string ToString()
        {
            return $"api: {GameApiName}, version: {GameVersion}";
        }

        [Serializable]
        private class NetworkKeys
        {
            public string networkAppKey;
            public string voiceAppKey;
        }

        [Serializable]
        private class AdminData
        {
            public string label;
            public string oculusId;
        }

#if UNITY_EDITOR
        public void SetApiType(ApiType apiType)
        {
            this.apiType = apiType;
            SaveAppConfig();
        }

        public void SetGameVersion(string bundleVersion, int bundleVersionCode)
        {
            gameVersion = $"{bundleVersion}.{bundleVersionCode}";
            SaveAppConfig();
        }

        public void SetFirebaseBaseUri(string baseUri)
        {
            firebaseBaseUri = baseUri;
            SaveAppConfig();
        }

        public void SetNetworkKeys()
        {
            var keys = apiType == ApiType.Prod ? prodNetworkKeys : devNetworkKeys;
            PhotonAppSettings.Global.AppSettings.AppIdFusion = keys.networkAppKey;
            PhotonAppSettings.Global.AppSettings.AppIdVoice = keys.voiceAppKey;
            EditorUtility.SetDirty(PhotonAppSettings.Global);
            AssetDatabase.SaveAssetIfDirty(PhotonAppSettings.Global);
        }

        private void SaveAppConfig()
        {
            EditorUtility.SetDirty(this);
            AssetDatabase.SaveAssetIfDirty(this);
        }
#endif
    }
}