using System.Threading;
using Cysharp.Threading.Tasks;
using Firebase;
using Modules.Core;

namespace Modules.Firebase
{
    internal class FirebaseService : IFirebaseService
    {
        private readonly IAsyncReactiveProperty<bool> initialized = new AsyncReactiveProperty<bool>(false);

        public IReadOnlyAsyncReactiveProperty<bool> Initialized => initialized;

        public async UniTask<Result> Initialize(CancellationToken cancellationToken)
        {
            var status = await FirebaseApp.CheckAndFixDependenciesAsync().AsUniTask().AttachExternalCancellation(cancellationToken);
            SetInitializationStatus(status == DependencyStatus.Available);

            Logger.FirebaseService.Debug("Initializing Firebase service. Status: {0}", initialized.Value ? "ok" : "fail");
            return initialized.Value ? Result.Ok() : Result.Fail(new Error("FirebaseService.Initialize", "Firebase service is not initialized"));
        }

        private void SetInitializationStatus(bool ok)
        {
            if (initialized.Value != ok)
            {
                initialized.Value = ok;
            }
        }
    }
}