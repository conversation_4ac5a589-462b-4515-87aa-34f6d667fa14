namespace Modules.Oculus
{
    public class Product
    {
        public readonly string sku;
        public readonly string name;
        public readonly string price;
        public readonly bool isConsumable;

        public Product(string sku, string name, string price, bool isConsumable)
        {
            this.sku = sku;
            this.name = name;
            this.price = price;
            this.isConsumable = isConsumable;
        }

        public override string ToString()
        {
            return $"sku: {sku}, name: {name}, price: {price}, isConsumable: {isConsumable}";
        }
    }
}