using System;
using UnityEngine;

namespace Modules.UI
{
    public abstract class WidgetBase : UIView
    {
        [SerializeField] private AudioConfig audioConfig;

        protected override void PlayClickAudio(Transform node)
        {
            if (!audioConfig.IsPlayClick)
            {
                return;
            }

            base.PlayClickAudio(node);
        }

        protected override void PlayHoverAudio(Transform node)
        {
            if (!audioConfig.IsPlayHover)
            {
                return;
            }

            base.PlayHoverAudio(node);
        }

        [Serializable]
        public class AudioConfig
        {
            public bool IsPlayClick = true;
            public bool IsPlayHover = true;
        }
    }

    public abstract class WidgetBase<T> : WidgetBase
    {
        public T WidgetData { get; private set; }

        public virtual void SetWidgetData(T widgetData)
        {
            WidgetData = widgetData;
        }
    }
}