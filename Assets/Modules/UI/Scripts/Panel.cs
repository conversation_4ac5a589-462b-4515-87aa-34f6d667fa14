using UnityEngine;
using VContainer;
using VContainer.Unity;

namespace Modules.UI
{
    public abstract class Panel : UIView
    {
        private IObjectResolver objectResolver;

        [Inject]
        private void Construct(IObjectResolver objectResolver)
        {
            this.objectResolver = objectResolver;
        }

        protected TWidget CreateWidget<TWidget>(TWidget prefab, Transform node) where TWidget : WidgetBase
        {
            var widget = Instantiate(prefab, node);

            if (objectResolver != null)
            {
                objectResolver.InjectGameObject(widget.gameObject);
            }

            return widget;
        }

        protected TWidget CreateWidget<TWidget, TWidgetData>(TWidget prefab, Transform node) where TWidget : WidgetBase<TWidgetData>
        {
            var widget = Instantiate(prefab, node);

            if (objectResolver != null)
            {
                objectResolver.InjectGameObject(widget.gameObject);
            }

            return widget;
        }
    }
}