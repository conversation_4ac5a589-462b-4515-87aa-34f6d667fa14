{"name": "Modules.UnityGameServices", "rootNamespace": "", "references": ["Modules.Core", "Unity.Services.Core", "Unity.Services.Core.Environments", "Unity.Services.Authentication", "Unity.Services.Economy", "Unity.Services.CloudSave", "Unity.Services.Leaderboards", "Unity.Services.RemoteConfig", "Unity.Services.CloudCode", "<PERSON><PERSON><PERSON>", "UniTask"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}