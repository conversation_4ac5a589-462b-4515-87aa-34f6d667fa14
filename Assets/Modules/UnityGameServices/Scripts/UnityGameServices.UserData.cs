using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using Modules.Core;
using Unity.Services.CloudSave;

namespace Modules.UnityGameServices
{
    internal partial class UnityGameServices
    {
        private Unity.Services.CloudSave.Internal.IPlayerDataService PlayerDataService => CloudSaveService.Instance.Data.Player;

        public async UniTask<Result<CloudData>> GetUserData(HashSet<string> keys, CancellationToken cancellationToken)
        {
            try
            {
                var data = await PlayerDataService.LoadAsync(keys).AsUniTask().AttachExternalCancellation(cancellationToken);
                return Result<CloudData>.Ok(new CloudData(data));
            }
            catch (OperationCanceledException exception)
            {
                return LogWarnAndGetResult<CloudData>(new Error(CancelOperationCode, exception.Message));
            }
            catch (Exception exception)
            {
                return LogFatalAndGetResult<CloudData>(new Error("GetUserData", exception.Message));
            }
        }

        public async UniTask<Result> SetUserData(Dictionary<string, object> data, CancellationToken cancellationToken)
        {
            try
            {
                await PlayerDataService.SaveAsync(data).AsUniTask().AttachExternalCancellation(cancellationToken);
                return Result.Ok();
            }
            catch (OperationCanceledException exception)
            {
                return LogWarnAndGetResult(new Error(CancelOperationCode, exception.Message));
            }
            catch (Exception exception)
            {
                return LogFatalAndGetResult(new Error("SetUserData", exception.Message));
            }
        }
    }
}