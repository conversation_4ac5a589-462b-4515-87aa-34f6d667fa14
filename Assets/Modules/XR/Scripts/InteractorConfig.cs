using System;
using Modules.Core;
using UnityEngine;

namespace Modules.XR
{
    public class InteractorConfig : Config
    {
        [SerializeField] private string title;

        [SerializeField] private RayConfig leftRay;

        [SerializeField] private RayConfig rightRay;

        public string Title => title;
        public RayConfig LeftRay => leftRay;
        public RayConfig RightRay => rightRay;

        [Serializable]
        public class Config
        {
            public bool Override;
            public bool Enable;
        }

        [Serializable]
        public class RayConfig : Config
        {
            public bool Visible;
            public float Length;
            public float Width;
        }
    }
}