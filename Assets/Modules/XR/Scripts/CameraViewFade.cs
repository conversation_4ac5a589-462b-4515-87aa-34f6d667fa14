using Cysharp.Threading.Tasks;
using DG.Tweening;
using Modules.Core;
using UnityEngine;

namespace Modules.XR
{
    internal class CameraViewFade : Actor
    {
        [SerializeField] private Transform fadeObject;
        [SerializeField] private MeshRenderer fadeRenderer;
        [SerializeField] private CameraViewFadeConfig config;

        private Sequence sequence;

        private void Awake()
        {
#if UNITY_EDITOR
            fadeRenderer.sharedMaterial = Instantiate(fadeRenderer.sharedMaterial);
#endif
        }

        private void OnDisable()
        {
            sequence?.Kill();
        }

        public async UniTask FadeInAsync(float duration)
        {
            if (gameObject.activeSelf)
            {
                return;
            }

            Show();
            sequence?.Kill();

            if (duration == 0)
            {
                fadeObject.localScale = config.MinScale * Vector3.one;
                fadeRenderer.sharedMaterial.color = config.FadeInColor;
            }
            else
            {
                fadeObject.localScale = config.MaxScale * Vector3.one;
                fadeRenderer.sharedMaterial.color = config.FadeOutColor;

                sequence = DOTween.Sequence();
                await sequence
                    .Append(fadeObject.DOScale(config.MinScale * Vector3.one, duration).SetEase(config.InEase))
                    .Insert(0.5f * duration, fadeRenderer.sharedMaterial.DOColor(config.FadeInColor, 0.5f * duration).SetEase(config.InEase))
                    .ToUniTask();
            }
        }

        public async UniTask FadeOutAsync(float duration)
        {
            if (!gameObject.activeSelf)
            {
                return;
            }

            Show();
            sequence?.Kill();

            if (duration == 0)
            {
                fadeObject.localScale = config.MaxScale * Vector3.one;
                fadeRenderer.sharedMaterial.color = config.FadeOutColor;
            }
            else
            {
                fadeObject.localScale = config.MinScale * Vector3.one;
                fadeRenderer.sharedMaterial.color = config.FadeInColor;

                sequence?.Kill();
                sequence = DOTween.Sequence();
                await sequence
                    .Append(fadeObject.DOScale(config.MaxScale * Vector3.one, duration).SetEase(config.OutEase))
                    .Insert(0.5f * duration, fadeRenderer.sharedMaterial.DOColor(config.FadeOutColor, 0.5f * duration).SetEase(config.OutEase))
                    .OnComplete(Hide)
                    .ToUniTask();
            }
        }

        [ContextMenu("FadeIn")]
        private void FadeIn()
        {
            FadeInAsync(1).Forget();
        }

        [ContextMenu("FadeOut")]
        private void FadeOut()
        {
            FadeOutAsync(1).Forget();
        }
    }
}