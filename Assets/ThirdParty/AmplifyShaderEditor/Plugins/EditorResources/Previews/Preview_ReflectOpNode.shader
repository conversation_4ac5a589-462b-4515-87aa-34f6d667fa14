Shader "Hidden/ReflectOpNode"
{
	Properties
	{
		_A ("_Incident", 2D) = "white" {}
		_B ("_Normal", 2D) = "white" {}
	}
	SubShader
	{
		Pass
		{
			CGPROGRAM
			#include "UnityCG.cginc"
			#pragma vertex vert_img
			#pragma fragment frag

			sampler2D _A;
			sampler2D _B;

			float4 frag(v2f_img i) : SV_Target
			{
				float4 a = tex2D( _A, i.uv );
				float4 b = tex2D( _B, i.uv );
				return reflect(a, b);
			}
			ENDCG
		}
	}
}
