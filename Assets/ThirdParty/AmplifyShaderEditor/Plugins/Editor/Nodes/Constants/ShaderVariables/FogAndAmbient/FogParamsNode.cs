// Amplify Shader Editor - Visual Shader Editing Tool
// Copyright (c) Amplify Creations, Lda <<EMAIL>>

using System;
namespace AmplifyShaderEditor
{
	[Serializable]
	[NodeAttributes( "Fog Params", "Lighting", "Parameters for fog calculation" )]
	public sealed class FogParamsNode : ConstVecShaderVariable
	{
		protected override void CommonInit( int uniqueId )
		{
			base.CommonInit( uniqueId );
			ChangeOutputName( 1, "Density/Sqrt(Ln(2))" );
			ChangeOutputName( 2, "Density/Ln(2)" );
			ChangeOutputName( 3, "-1/(End-Start)" );
			ChangeOutputName( 4, "End/(End-Start))" );
			m_value = "unity_FogParams";
			m_previewShaderGUID = "42abde3281b1848438c3b53443c91a1e";
		}

		public override void RefreshExternalReferences()
		{
			base.RefreshExternalReferences();
			if( !m_outputPorts[ 0 ].IsConnected )
			{
				m_outputPorts[ 0 ].Visible = false;
				m_sizeIsDirty = true;
			}
		}
	}
}
