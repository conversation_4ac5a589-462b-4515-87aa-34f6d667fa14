using System;
using System.Reactive.Linq;
using Cysharp.Threading.Tasks;
using Fusion;
using Game.Core;
using Game.Views.Projectiles;
using MessagePipe;
using Modules.Core;
using Modules.XR;
using UnityEngine;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;

namespace Game.Views.Guns
{
    public class RaycastGunActor : GunActor<RaycastGunView>
    {
        [SerializeField] private LayerMask hitMask;
        [SerializeField] private int damage;
        [SerializeField] private float maxDistance = 100;

        private int visibleFireCount;
        private ProjectilesPool projectilesPool;
        private IPublisher<RaycastGunDamageArgs> shootSignal;

        private Vector3 BarrelPosition => View.BarrelNode.position;
        private Vector3 BarrelDirection => View.BarrelNode.forward;

        public int Damage => damage;
        [Networked] private int FireCount { get; set; }
        [Networked] [Capacity(8)] private NetworkArray<ProjectileData> ProjectileData { get; }

        [Inject]
        private void Construct(IPublisher<RaycastGunDamageArgs> shootSignal, ProjectilesPool projectilesPool)
        {
            this.shootSignal = shootSignal;
            this.projectilesPool = projectilesPool;
        }

        protected override void HandleSelectEntered(SelectEnterEventArgs args)
        {
            base.HandleSelectEntered(args);
            var onFire = args.interactorObject.IsLeftHand() ? XRInput.OnLeftActivate : XRInput.OnRightActivate;
            onFire.Where(_ => HasStateAuthority && HasView).Where(obj => obj.performed).Subscribe(_ => Fire()).AddTo(DropCancellationToken);
        }

        public override void Spawned()
        {
            base.Spawned();
            visibleFireCount = FireCount;
        }

        public override void Render()
        {
            base.Render();

            if (!HasView)
            {
                return;
            }

            if (visibleFireCount < FireCount)
            {
                AudioClient.Play(AudioKeys.GunFire, BarrelPosition, destroyCancellationToken);
            }

            for (var i = visibleFireCount; i < FireCount; i++)
            {
                var data = ProjectileData[i % ProjectileData.Length];
                var projectile = projectilesPool.Get();
                projectile.Fire(BarrelPosition, data.targetPosition, data.impactType);
            }

            visibleFireCount = FireCount;
        }

        private void Fire()
        {
            if (!HasView)
            {
                return;
            }

            bool isPlayerHit;
            Vector3 targetPosition;

            if (Physics.Raycast(BarrelPosition, BarrelDirection, out var hit, maxDistance, hitMask))
            {
                targetPosition = hit.point;
                isPlayerHit = hit.collider.gameObject.layer == Layers.XRPlayer;
                shootSignal.Publish(new RaycastGunDamageArgs(this, hit));
            }
            else
            {
                isPlayerHit = false;
                targetPosition = BarrelPosition + BarrelDirection * maxDistance;
            }

            var impactType = isPlayerHit ? ProjectileImpactType.Player : ProjectileImpactType.Default;
            ProjectileData.Set(FireCount % ProjectileData.Length, new ProjectileData(targetPosition, impactType));
            FireCount++;
        }
    }
}