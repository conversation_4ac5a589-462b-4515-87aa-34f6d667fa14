using System;
using System.Reactive.Subjects;
using Game.Views.Items;
using Modules.XR;
using UnityEngine.XR.Interaction.Toolkit;

namespace Game.Views.PlayerUI.Backpack
{
    public class ActivationTrigger : XRSimpleSocketInteractor
    {
        private bool isHovered;

        private readonly ISubject<IXRInteractor> onHoverStarted = new Subject<IXRInteractor>();
        private readonly ISubject<IXRInteractor> onHoverEnded = new Subject<IXRInteractor>();
        private readonly ISubject<BackpackActor> onTriggered = new Subject<BackpackActor>();

        public IObservable<IXRInteractor> OnHoverStarted => onHoverStarted;
        public IObservable<IXRInteractor> OnHoverEnded => onHoverEnded;
        public IObservable<BackpackActor> OnTriggered => onTriggered;

        public void SetActive(bool isActive)
        {
            gameObject.SetActive(isActive);
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            isHovered = false;
        }

        protected override void OnHoverEntered(HoverEnterEventArgs args)
        {
            base.OnHoverEntered(args);
            if (!TryGetBackpackActor(args.interactableObject, out var actor) || !actor.HasStateAuthority)
            {
                return;
            }

            isHovered = actor.IsGrabbed;
            if (isHovered)
            {
                onHoverStarted.OnNext(actor.SelectInteractor);
            }
        }

        protected override void OnHoverExited(HoverExitEventArgs args)
        {
            base.OnHoverExited(args);
            if (!isHovered)
            {
                return;
            }

            onHoverEnded.OnNext(args.interactorObject);
            isHovered = false;
        }

        protected override void OnSelectEntered(SelectEnterEventArgs args)
        {
            base.OnSelectEntered(args);
            if (isHovered && TryGetBackpackActor(args.interactableObject, out var actor) && actor.HasStateAuthority)
            {
                onTriggered.OnNext(actor);
            }
        }

        public override bool CanSelect(IXRSelectInteractable interactable)
        {
            return base.CanSelect(interactable) && !interactable.isSelected && isHovered;
        }

        private static bool TryGetBackpackActor(IXRInteractable interactable, out BackpackActor actor)
        {
            return interactable.transform.TryGetComponent(out actor);
        }
    }
}