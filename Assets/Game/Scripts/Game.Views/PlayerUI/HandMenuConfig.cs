using Modules.Core;
using UnityEngine;

namespace Game.Views.PlayerUI
{
    public class HandMenuConfig : Config
    {
        [SerializeField] private float orbitRadius;
        [SerializeField] private float rotationOffset;
        [SerializeField] private float translationOffset;
        [SerializeField] private int interactableCount;
        [SerializeField] private float bubbleRadius;
        [SerializeField] private float angleStep;

        public float OrbitRadius => orbitRadius;
        public float RotationOffset => rotationOffset;
        public float TranslationOffset => translationOffset;

        public int InteractableCount => interactableCount;
        public float BubbleRadius => bubbleRadius;
        public float AngleStep => angleStep;
    }
}