using System;
using System.Reactive.Subjects;
using System.Threading;
using Cysharp.Threading.Tasks;
using Game.Views.Players;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.PlayerUI
{
    public class HandMenu : Actor
    {
        [SerializeField] private InteractableWidget interactableWidgetPrefab;
        [SerializeField] private HandType handType;
        [SerializeField] protected int extraSlotCount;
        [SerializeField] private Transform interactableWidgetNode;

        private readonly ISubject<InteractableWidget> onClicked = new Subject<InteractableWidget>();
        private readonly ISubject<InteractableWidget> onGrabbed = new Subject<InteractableWidget>();

        private IObjectResolver objectResolver;
        private ComponentPool<InteractableWidget> interactableWidgetPool;
        protected CancellationTokenSource interactableWidgetCancellationTokenSource;

        protected HandMenuConfig Config { get; private set; }

        public IObservable<InteractableWidget> OnClicked => onClicked;
        public IObservable<InteractableWidget> OnGrabbed => onGrabbed;

        [Inject]
        private void Construct(HandMenuConfig config, IObjectResolver objectResolver)
        {
            Config = config;
            this.objectResolver = objectResolver;
        }

        private void Awake()
        {
            interactableWidgetPool = new ComponentPool<InteractableWidget>(interactableWidgetPrefab, interactableWidgetNode, objectResolver, Config.InteractableCount);

            ShowInteractableWidgets();
        }

        private void OnDestroy()
        {
            interactableWidgetCancellationTokenSource.CancelAndDispose();
        }

        public void SetActiveInteractable(ActiveInteractable activeInteractable)
        {
            foreach (var widget in interactableWidgetPool.ActivePoolItems)
            {
                if (widget.Index == activeInteractable.index && widget.InteractableCode != activeInteractable.code)
                {
                    widget.SetInteractable(activeInteractable.code);
                    return;
                }
            }
        }

        public InteractableWidget GetEmptyWidget()
        {
            foreach (var widget in interactableWidgetPool.ActivePoolItems)
            {
                if (widget.Interactable == null)
                {
                    return widget;
                }
            }

            return null;
        }

        public void SetInteractableWidgetNodeActive(bool active)
        {
            interactableWidgetNode.gameObject.SetActive(active);
        }

        public void UpdatePose(Vector3 wristPosition, Vector3 elbowPosition, Quaternion wristLocalRotation)
        {
            var vector = elbowPosition - wristPosition;
            var direction = vector.normalized;
            var position = wristPosition + Config.TranslationOffset * direction;
            var rotation = Quaternion.LookRotation(direction) * Quaternion.Euler(0, 0, -wristLocalRotation.eulerAngles.y);
            transform.SetPositionAndRotation(position, rotation);
        }

        public void SetScale(float scale)
        {
            transform.localScale = scale * Vector3.one;
        }

        protected virtual void ShowInteractableWidgets()
        {
        }

        protected void CreateInteractableWidget(int index, Vector3 position, Quaternion rotation)
        {
            var widget = interactableWidgetPool.Get();
            widget.Index = index;
            widget.HandType = handType;
            widget.BubbleDiameter = Config.BubbleRadius;
            widget.SetOriginLocalPositionAndRotation(position, rotation);
            widget.OnClicked.Subscribe(HandleClickInteractableWidget).AddTo(interactableWidgetCancellationTokenSource.Token);
            widget.OnGrabbed.Subscribe(HandleGrabInteractableWidget).AddTo(interactableWidgetCancellationTokenSource.Token);
        }

        protected virtual void CreateNewWidget(Vector3 position, Quaternion rotation)
        {
        }

        private void HandleClickInteractableWidget(InteractableWidget widget)
        {
            onClicked.OnNext(widget);
        }

        private void HandleGrabInteractableWidget(InteractableWidget widget)
        {
            onGrabbed.OnNext(widget);
        }
    }
}