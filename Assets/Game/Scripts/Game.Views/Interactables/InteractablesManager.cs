using Game.Views.Badges;
using Game.Views.Consumeables;
using Game.Views.Guns;
using Game.Views.InteractablesCore;
using Game.Views.Items;
using Game.Views.Weapons;
using Game.Views.Wings;
using UnityEngine;
using VContainer;

namespace Game.Views.Interactables
{
    public class InteractablesManager : MonoBehaviour
    {
        private GunsManager gunsManager;
        private WingsManager wingsManager;
        private ItemsManager itemsManager;
        private BadgesManager badgesManager;
        private WeaponsManager weaponsManager;
        private ConsumablesManager consumablesManager;

        [Inject]
        private void Construct(
            GunsManager gunsManager,
            ItemsManager itemsManager,
            WingsManager wingsManager,
            BadgesManager badgesManager,
            WeaponsManager weaponsManager,
            ConsumablesManager consumablesManager)
        {
            this.itemsManager = itemsManager;
            this.gunsManager = gunsManager;
            this.wingsManager = wingsManager;
            this.badgesManager = badgesManager;
            this.weaponsManager = weaponsManager;
            this.consumablesManager = consumablesManager;
        }

        public InteractableView CreateView(string code, Transform parent)
        {
            if (weaponsManager.TryCreateView(code, parent, out var weaponView))
            {
                return weaponView;
            }

            if (gunsManager.TryCreateView(code, parent, out var gunView))
            {
                return gunView;
            }

            if (wingsManager.TryCreateView(code, parent, out var wingsView))
            {
                return wingsView;
            }

            if (badgesManager.TryCreateView(code, parent, out var badgeView))
            {
                return badgeView;
            }

            if (itemsManager.TryCreateView(code, parent, out var itemView))
            {
                return itemView;
            }

            if (consumablesManager.TryCreateView(code, parent, out var consumeableView))
            {
                return consumeableView;
            }

            return null;
        }

        public void DestroyView(InteractableView view)
        {
            if (weaponsManager.DestroyView(view))
            {
                return;
            }

            if (gunsManager.DestroyView(view))
            {
                return;
            }

            if (wingsManager.DestroyView(view))
            {
                return;
            }

            if (itemsManager.DestroyView(view))
            {
                return;
            }

            if (consumablesManager.DestroyView(view))
            {
                return;
            }

            if (badgesManager.DestroyView(view))
            {
            }
        }

        public InteractableActor CreateActor(string code, Pose pose = default)
        {
            if (weaponsManager.TryCreateActor(code, pose, out var weaponActor))
            {
                return weaponActor;
            }

            if (gunsManager.TryCreateActor(code, pose, out var gunsActor))
            {
                return gunsActor;
            }

            if (wingsManager.TryCreateActor(code, pose, out var wingsActor))
            {
                return wingsActor;
            }

            if (itemsManager.TryCreateActor(code, pose, out var itemsActor))
            {
                return itemsActor;
            }

            if (consumablesManager.TryCreateActor(code, pose, out var consumablesActor))
            {
                return consumablesActor;
            }

            return null;
        }

        public InteractableActor CreateActor(int id, Pose pose = default)
        {
            if (weaponsManager.TryCreateActor(id, pose, out var weaponActor))
            {
                return weaponActor;
            }

            if (gunsManager.TryCreateActor(id, pose, out var gunsActor))
            {
                return gunsActor;
            }

            if (wingsManager.TryCreateActor(id, pose, out var wingsActor))
            {
                return wingsActor;
            }

            if (itemsManager.TryCreateActor(id, pose, out var itemsActor))
            {
                return itemsActor;
            }

            if (consumablesManager.TryCreateActor(id, pose, out var consumablesActor))
            {
                return consumablesActor;
            }

            return null;
        }
    }
}