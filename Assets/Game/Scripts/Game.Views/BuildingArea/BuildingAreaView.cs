using Modules.Core;
using UnityEngine;

namespace Game.Views.BuildingArea
{
    public class BuildingAreaView : Actor
    {
        [SerializeField] private LineRenderer lineRenderer;

        public void Initialize(Vector3 position, Vector3 size)
        {
            transform.position = position;
            transform.localScale = size;
            DrawBounds(size);
        }

        public bool IsPointInBounds(Vector3 point)
        {
            var extents = 0.5f * transform.localScale;
            var min = transform.position - extents;
            var max = transform.position + extents;

            return point.x >= min.x && point.x <= max.x &&
                   point.y >= min.y && point.y <= max.y &&
                   point.z >= min.z && point.z <= max.z;
        }

        private void DrawBounds(Vector3 size)
        {
            var halfSize = size * 0.5f;

            var corners = new Vector3[8];
            corners[0] = transform.position + new Vector3(-halfSize.x, -halfSize.y, -halfSize.z); // Bottom-back-left
            corners[1] = transform.position + new Vector3(halfSize.x, -halfSize.y, -halfSize.z); // Bottom-back-right
            corners[2] = transform.position + new Vector3(halfSize.x, -halfSize.y, halfSize.z); // Bottom-front-right
            corners[3] = transform.position + new Vector3(-halfSize.x, -halfSize.y, halfSize.z); // Bottom-front-left

            corners[4] = transform.position + new Vector3(-halfSize.x, halfSize.y, -halfSize.z); // Top-back-left
            corners[5] = transform.position + new Vector3(halfSize.x, halfSize.y, -halfSize.z); // Top-back-right
            corners[6] = transform.position + new Vector3(halfSize.x, halfSize.y, halfSize.z); // Top-front-right
            corners[7] = transform.position + new Vector3(-halfSize.x, halfSize.y, halfSize.z); // Top-front-left

            Vector3[] edges =
            {
                // Bottom square
                corners[0], corners[1],
                corners[1], corners[2],
                corners[2], corners[3],
                corners[3], corners[0],

                corners[0], corners[4], // vert up

                // Top square
                corners[4], corners[5],
                corners[5], corners[6],
                corners[6], corners[7],
                corners[7], corners[4],

                corners[4], corners[5], // hor
                corners[5], corners[1], // vert down

                corners[1], corners[2], // hor
                corners[2], corners[6], // vert up

                corners[6], corners[7], // hor
                corners[7], corners[3] // vert down
            };

            lineRenderer.loop = false;
            lineRenderer.positionCount = edges.Length;
            lineRenderer.SetPositions(edges);
        }
    }
}