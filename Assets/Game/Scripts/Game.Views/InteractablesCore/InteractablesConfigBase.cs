using System.Collections.Generic;
using Modules.Core;
using UnityEngine;

namespace Game.Views.InteractablesCore
{
    public abstract class InteractablesConfigBase<TData> : Config where TData : InteractableData
    {
        [SerializeField] private List<TData> interactablesDataList;
        [SerializeField] private int dropTimeout = 10;

        public int DropTimeout => dropTimeout;

        public List<TData> InteractablesDataList => interactablesDataList;

        public TData GetInteractableData(int id)
        {
            return interactablesDataList.Find(x => x.Id == id);
        }

        public TData GetInteractableData(string code)
        {
            return interactablesDataList.Find(x => x.Code == code);
        }

        public bool TryGetCode(int id, out string code)
        {
            var data = interactablesDataList.Find(x => x.Id == id);
            code = data?.Code;
            return data != null;
        }

        public bool TryGetId(string code, out int id)
        {
            var data = interactablesDataList.Find(x => x.Code == code);
            id = data?.Id ?? 0;
            return data != null;
        }

        public TData GetInteractableData<TView>() where TView : InteractableView
        {
            foreach (var interactableData in interactablesDataList)
            {
                if (interactableData.ViewPrefab is TView)
                {
                    return interactableData;
                }
            }

            return null;
        }

        public bool HasActor<TActor>() where TActor : InteractableActor
        {
            foreach (var interactableData in interactablesDataList)
            {
                if (interactableData.ActorPrefab.TryGetComponent<TActor>(out _))
                {
                    return true;
                }
            }

            return false;
        }

        public bool Has(int id)
        {
            return interactablesDataList.Exists(x => x.Id == id);
        }

        public bool Has(string code)
        {
            return interactablesDataList.Exists(x => x.Code == code);
        }
    }
}