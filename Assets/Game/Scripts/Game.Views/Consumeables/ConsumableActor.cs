using System.Collections.Generic;
using Fusion;
using Game.Views.InteractablesCore;
using MessagePipe;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.Consumeables
{
    public class ConsumableActor : InteractableActor
    {
        [Header("Consumeable")]
        [SerializeField] protected Transform viewParent;

        private IPublisher<ConsumableConsumeArgs> consumePublisher;

        protected ConsumablesManager consumablesManager;
        protected ConsumablesConfig consumablesConfig;
        protected IAudioClient audioClient;

        [Inject]
        private void Construct(ConsumablesManager consumablesManager, ConsumablesConfig consumablesConfig, IAudioClient audioClient, IPublisher<ConsumableConsumeArgs> consumePublisher)
        {
            this.consumablesManager = consumablesManager;
            this.consumablesConfig = consumablesConfig;
            this.consumePublisher = consumePublisher;
            this.audioClient = audioClient;
        }

        public override void Spawned()
        {
            base.Spawned();
            InitializeView();
        }

        public override void Despawned(NetworkRunner runner, bool hasState)
        {
            base.Despawned(runner, hasState);
            UninitializeView();
        }

        protected override void SetColliders(List<Collider> colliderList)
        {
        }

        protected override void ClearColliders()
        {
        }

        public virtual void DestroyActor()
        {
            consumablesManager.DestroyActor(this);
        }

        private void OnTriggerEnter(Collider collider)
        {
            if (!HasStateAuthority)
            {
                return;
            }

            consumePublisher.Publish(new ConsumableConsumeArgs(this, collider));
        }
    }

    public class ConsumableActor<TView> : ConsumableActor where TView : InteractableView
    {
        protected TView View { get; private set; }

        protected override void InitializeView()
        {
            base.InitializeView();
            var view = consumablesManager.CreateView(InteractableId, viewParent);

            if (view is TView currentView)
            {
                View = currentView;
            }
            else
            {
                consumablesManager.DestroyView(view);
            }

            RegisterInteractable();
        }

        protected override void UninitializeView()
        {
            base.UninitializeView();
            UnregisterInteractable();
            consumablesManager.DestroyView(View);
            View = null;
        }
    }
}