using Modules.Core;
using UnityEngine;
using VContainer;
using VContainer.Unity;

namespace Game.Views.Consumeables
{
    public class ConsumablesMonoInstaller : MonoInstaller
    {
        [SerializeField] private ConsumablesManager consumablesManagerPrefab;
        [SerializeField] private ConsumablesConfig consumablesConfig;

        public override void Install(IContainerBuilder builder, Transform node = null)
        {
            builder.RegisterComponentInNewPrefab(consumablesManagerPrefab, Lifetime.Singleton);
            builder.RegisterInstance(consumablesConfig);
        }
    }
}