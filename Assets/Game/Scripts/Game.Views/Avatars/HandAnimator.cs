using System.Collections.Generic;
using Game.Core.Data;
using UnityEngine;

namespace Game.Views.Avatars
{
    public class HandAnimator
    {
        private static readonly int LeftIndex = Animator.StringToHash("LeftIndex");
        private static readonly int LeftPinky = Animator.StringToHash("LeftPinky");
        private static readonly int LeftThumb = Animator.StringToHash("LeftThumb");
        private static readonly int RightIndex = Animator.StringToHash("RightIndex");
        private static readonly int RightPinky = Animator.StringToHash("RightPinky");
        private static readonly int RightThumb = Animator.StringToHash("RightThumb");

        private static readonly List<FingerPoses> Fingers = new()
        {
            FingerPoses.LeftIndex,
            FingerPoses.LeftPinky,
            FingerPoses.LeftThumb,
            FingerPoses.RightIndex,
            FingerPoses.RightPinky,
            FingerPoses.RightThumb
        };

        private readonly Animator animator;

        public HandAnimator(Animator animator)
        {
            this.animator = animator;
        }

        public void Render(FingerPoses currentFingerPoses)
        {
            foreach (var fingerPose in Fingers)
            {
                var (name, layer) = GetStateData(fingerPose);

                if (layer == -1)
                {
                    continue;
                }

                var time = GetStateTime(layer);
                var isPressed = currentFingerPoses.HasFlag(fingerPose);
                animator.SetFloat(name, isPressed ? 1 : -1);
                animator.Play(name, layer, time);
            }
        }

        private float GetStateTime(int layer)
        {
            return Mathf.Clamp(animator.GetCurrentAnimatorStateInfo(layer).normalizedTime, 0, 1);
        }

        private (int key, int layer) GetStateData(FingerPoses fingerPoses)
        {
            if (fingerPoses.HasFlag(FingerPoses.LeftIndex))
            {
                return (LeftIndex, 0);
            }

            if (fingerPoses.HasFlag(FingerPoses.LeftPinky))
            {
                return (LeftPinky, 2);
            }

            if (fingerPoses.HasFlag(FingerPoses.LeftThumb))
            {
                return (LeftThumb, 4);
            }

            if (fingerPoses.HasFlag(FingerPoses.RightIndex))
            {
                return (RightIndex, 1);
            }

            if (fingerPoses.HasFlag(FingerPoses.RightPinky))
            {
                return (RightPinky, 3);
            }

            if (fingerPoses.HasFlag(FingerPoses.RightThumb))
            {
                return (RightThumb, 5);
            }

            return (0, -1);
        }
    }
}