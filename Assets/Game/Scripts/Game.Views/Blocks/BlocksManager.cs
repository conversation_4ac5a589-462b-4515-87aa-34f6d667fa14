using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.Blocks
{
    public class BlocksManager : MonoBehaviour
    {
        [SerializeField] private BlockView blockViewPrefab;

        private IObjectResolver objectResolver;
        private ComponentPool<BlockView> blockViewPool;

        private ComponentPool<BlockView> BlockViewPool => blockViewPool ??= new ComponentPool<BlockView>(blockViewPrefab, transform, objectResolver);

        [Inject]
        private void Construct(IObjectResolver objectResolver)
        {
            this.objectResolver = objectResolver;
        }

        public BlockView CreateBlock(int id, bool isTextOff, Transform parent)
        {
            var view = BlockViewPool.Get();
            view.Initialize(id, isTextOff);
            view.SetParent(parent);
            return view;
        }

        public void DestroyBlock(BlockView blockView)
        {
            if (blockView != null)
            {
                BlockViewPool.Release(blockView);
            }
        }
    }
}