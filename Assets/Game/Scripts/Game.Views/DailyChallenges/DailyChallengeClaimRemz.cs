using System;

namespace Game.Views.DailyChallenges
{
    public class DailyChallengeClaimRemz : IDailyChallenge
    {
        public string Description { get; set; }
        public string RewardText { get; set; }
        public DailyChallengeType ChallengeType { get; set; }
        public DailyChallengeRewardType RewardType { get; set; }
        public int Progress { get; set; }
        public int Goal { get; set; }
        public bool IsClaimed { get; set; }
        public bool IsCompleted { get; set; }
        public bool IsClaimable { get; set; }
        public DateTimeOffset? NextClaim { get; set; }

        public DailyChallengeClaimRemz(
            string description,
            string rewardText,
            DailyChallengeType challengeType,
            DailyChallengeRewardType rewardType,
            bool canClaim,
            bool isClaimed,
            DateTimeOffset? nextClaim,
            int goal = 0
        )
        {
            Description = description;
            RewardText = rewardText;
            ChallengeType = challengeType;
            RewardType = rewardType;
            Progress = 0;
            Goal = goal;
            IsClaimed = isClaimed;
            IsCompleted = false;
            IsClaimable = canClaim;
            NextClaim = nextClaim;
        }
    }
}