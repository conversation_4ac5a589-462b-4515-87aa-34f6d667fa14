using Fusion;
using Modules.Network;

namespace Game.Views.Portals
{
    public class LevelPortalMasterClientObject : NetworkActor
    {
        [Networked] public float YPosition { get; private set; }

        public void SetYPosition(float yPosition)
        {
            SendRpcSafe(() => SetYPositionRpc(yPosition));
        }

        [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
        private void SetYPositionRpc(float yPosition)
        {
            YPosition = yPosition;
        }
    }
}