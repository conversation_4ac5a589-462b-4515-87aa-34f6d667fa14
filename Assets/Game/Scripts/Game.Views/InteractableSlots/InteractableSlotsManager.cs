using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.InteractableSlots
{
    public class InteractableSlotsManager : MonoBehaviour
    {
        [SerializeField] private InteractableSlotActor prefab;

        private IObjectResolver objectResolver;
        private ComponentPool<InteractableSlotActor> slotPool;

        private ComponentPool<InteractableSlotActor> SlotPool => slotPool ??= new ComponentPool<InteractableSlotActor>(prefab, transform, objectResolver);

        [Inject]
        private void Construct(IObjectResolver objectResolver)
        {
            this.objectResolver = objectResolver;
        }

        public InteractableSlotActor CreateSlot(Pose pose, string code)
        {
            var slot = SlotPool.Get();
            slot.SetPose(pose);
            slot.SetInteractable(code);
            return slot;
        }

        public void DestroySlot(InteractableSlotActor slot)
        {
            SlotPool.Release(slot);
        }
    }
}