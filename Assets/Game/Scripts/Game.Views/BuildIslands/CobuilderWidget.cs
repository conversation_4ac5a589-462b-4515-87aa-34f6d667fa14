using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using Modules.Core;
using Modules.UI;
using TMPro;
using UnityEngine;

namespace Game.Views.BuildIslands
{
    public class CobuilderWidget : ScrollWidget<CobuilderData>
    {
        [SerializeField] private TMP_Text playerNameText;
        [SerializeField] private SimpleButton addPlayerButton;
        [SerializeField] private SimpleButton removePlayerButton;

        private CancellationTokenSource disableCancellationTokenSource;

        protected override void OnEnable()
        {
            base.OnEnable();
            disableCancellationTokenSource = new CancellationTokenSource();
            addPlayerButton.OnClicked.Subscribe(_ => HandleOption1Clicked()).AddTo(disableCancellationTokenSource.Token);
            removePlayerButton.OnClicked.Subscribe(_ => HandleOption2Clicked()).AddTo(disableCancellationTokenSource.Token);
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            disableCancellationTokenSource.CancelAndDispose();
        }

        public override void SetWidgetData(CobuilderData widgetData)
        {
            base.SetWidgetData(widgetData);
            playerNameText.text = widgetData.playerName;

            if (widgetData.isOwner)
            {
                addPlayerButton.gameObject.SetActive(widgetData.isRequest);
                removePlayerButton.gameObject.SetActive(true);
            }
            else
            {
                addPlayerButton.gameObject.SetActive(false);
                removePlayerButton.gameObject.SetActive(widgetData.isLocalPlayer);
            }
        }
    }
}