using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.Monsters
{
    public class MonsterRagdoll : Actor
    {
        private const float DisableDelay = 10;

        [SerializeField] private Rigidbody rootRigidbody;
        [SerializeField] private Transform avatarNode;

        private MonstersManager monstersManager;
        private CancellationTokenSource disableCancellationToken;

        public byte Code { get; private set; }

        [Inject]
        private void Construct(MonstersManager monstersManager)
        {
            this.monstersManager = monstersManager;
        }

        private void OnEnable()
        {
            avatarNode.gameObject.SetActive(false);

            disableCancellationToken = new CancellationTokenSource();
            UniTaskAsyncEnumerable.Timer(TimeSpan.FromSeconds(DisableDelay)).Subscribe(_ => monstersManager.DestroyRagdoll(this)).AddTo(disableCancellationToken.Token);
        }

        private void OnDisable()
        {
            avatarNode.gameObject.SetActive(false);

            disableCancellationToken.CancelAndDispose();
        }

        public void SetPose(Transform[] nodeList)
        {
            var selfNodeList = avatarNode.GetComponentsInChildren<Transform>(true);
            var length = Mathf.Min(nodeList.Length, selfNodeList.Length);

            for (var i = 0; i < length; i++)
            {
                selfNodeList[i].SetLocalPositionAndRotation(nodeList[i].localPosition, nodeList[i].localRotation);
            }
        }

        public void AddForce(Vector3 force)
        {
            avatarNode.gameObject.SetActive(true);
            rootRigidbody.AddForce(force, ForceMode.Acceleration);
        }

        public void SetCode(byte code)
        {
            Code = code;
        }
    }
}