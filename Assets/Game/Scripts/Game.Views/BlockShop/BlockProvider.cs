using Game.Views.Blocks;
using Game.Views.Voxels;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.BlockShop
{
    public class BlockProvider : Actor
    {
        [SerializeField] private Transform parent;

        private BlocksManager blocksManager;
        private BlockView blockView;

        [Inject]
        private void Construct(BlocksManager blocksManager)
        {
            this.blocksManager = blocksManager;
        }

        private void OnDestroy()
        {
            Destroy();
        }

        public void Create(BlockData data, int scale, Transform parent = null)
        {
            Destroy();
            blockView = blocksManager?.CreateBlock(data.id, true, parent ?? this.parent);
            blockView?.SetScale(scale);
        }

        public void Destroy()
        {
            blocksManager?.DestroyBlock(blockView);
            blockView = null;
        }
    }
}