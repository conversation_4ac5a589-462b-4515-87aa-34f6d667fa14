using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using Game.Core.Data;
using Game.Views.Shared;
using Modules.Core;
using UnityEngine;

namespace Game.Views.GadgetShop
{
    public class GadgetShopView : Actor
    {
        [SerializeField] private SimpleButton nextButton;
        [SerializeField] private SimpleButton previousButton;
        [SerializeField] private GadgetShopContainer gadgetShopContainer;
        [SerializeField] private List<GadgetCategoryButton> categoryButtonList;

        private bool isInitialized;
        private CancellationTokenSource disableCancellationTokenSource;

        public IObservable<GadgetInventoryView> OnClicked => gadgetShopContainer.OnClicked;

        private void OnEnable()
        {
            if (isInitialized)
            {
                SubscribeScreen();
                SelectCategoryIfNone();
            }
        }

        private void OnDisable()
        {
            disableCancellationTokenSource.CancelAndDispose();
        }

        public void Initialize(List<GadgetShopData> gadgetShopDatList)
        {
            gadgetShopContainer.Initialize(gadgetShopDatList);

            if (IsActiveSelfAndInHierarchy)
            {
                SubscribeScreen();
                SelectCategoryIfNone();
            }

            isInitialized = true;
        }

        public void Refresh()
        {
            if (!IsActiveSelfAndInHierarchy)
            {
                return;
            }

            SelectCategory(gadgetShopContainer.CurrentCategory);
        }

        private void SubscribeScreen()
        {
            disableCancellationTokenSource.CancelAndDispose();
            disableCancellationTokenSource = new CancellationTokenSource();

            nextButton.OnClicked.Subscribe(_ => HandleNextButton()).AddTo(disableCancellationTokenSource.Token);
            previousButton.OnClicked.Subscribe(_ => HandlePreviousButton()).AddTo(disableCancellationTokenSource.Token);
            categoryButtonList.ForEach(b => b.OnClicked.Subscribe(_ => HandleCategoryClicked(b)).AddTo(disableCancellationTokenSource.Token));
        }

        private void SelectCategoryIfNone()
        {
            if (gadgetShopContainer.CurrentCategory != InventoryCategory.None)
            {
                return;
            }

            categoryButtonList[0].Click();
        }

        private void SelectCategory(InventoryCategory category)
        {
            gadgetShopContainer.SelectCategory(category);
        }

        private void HandleCategoryClicked(GadgetCategoryButton button)
        {
            if (gadgetShopContainer.CurrentCategory == button.Category)
            {
                return;
            }

            SelectCategory(button.Category);
        }

        private void HandleNextButton()
        {
            gadgetShopContainer.SetNextPage();
        }

        private void HandlePreviousButton()
        {
            gadgetShopContainer.SetPreviousPage();
        }
    }
}