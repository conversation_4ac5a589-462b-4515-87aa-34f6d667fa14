using System;
using System.Collections.Generic;
using System.Reactive.Subjects;
using System.Threading;
using Cysharp.Threading.Tasks;
using Game.Core.Data;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.GadgetShop
{
    public class GadgetShopContainer : Actor
    {
        [SerializeField] private GadgetInventoryView gadgetInventoryViewPrefab;
        [SerializeField] private int itemsPerPage;
        [SerializeField] private float offset;

        private int currentPage;
        private IObjectResolver objectResolver;
        private ComponentPool<GadgetInventoryView> gadgetInventoryViewPool;
        private CancellationTokenSource gadgetInventoryViewCancellationToken;

        private readonly List<GadgetShopData> gadgetShopDataList = new();
        private readonly Dictionary<int, List<GadgetShopData>> gadgetShopDataPages = new();
        private readonly ISubject<GadgetInventoryView> onClicked = new Subject<GadgetInventoryView>();

        public InventoryCategory CurrentCategory { get; private set; }
        public IObservable<GadgetInventoryView> OnClicked => onClicked;

        [Inject]
        private void Construct(IObjectResolver objectResolver)
        {
            this.objectResolver = objectResolver;
        }

        private void Awake()
        {
            gadgetInventoryViewPool = new ComponentPool<GadgetInventoryView>(gadgetInventoryViewPrefab, transform, objectResolver, itemsPerPage);
        }

        private void OnDestroy()
        {
            gadgetInventoryViewCancellationToken.CancelAndDispose();
        }

        public void Initialize(List<GadgetShopData> gadgetShopDataList)
        {
            this.gadgetShopDataList.Clear();
            this.gadgetShopDataList.AddRange(gadgetShopDataList);
        }

        public void SelectCategory(InventoryCategory category)
        {
            if (gadgetShopDataList == null)
            {
                return;
            }

            currentPage = 0;
            CurrentCategory = category;
            InitializeInventoryPages(category);
            Render();
        }

        public void SetNextPage()
        {
            MoveCurrentPage(1);
            Render();
        }

        public void SetPreviousPage()
        {
            MoveCurrentPage(-1);
            Render();
        }

        private void MoveCurrentPage(int offset)
        {
            var total = gadgetShopDataPages.Count > 0 ? gadgetShopDataPages.Count : 1;
            currentPage = (currentPage + total + offset) % total;
        }

        private void Render()
        {
            gadgetInventoryViewCancellationToken.CancelAndDispose();
            gadgetInventoryViewCancellationToken = new CancellationTokenSource();
            gadgetInventoryViewPool.ReleaseAll();

            if (gadgetShopDataPages.Count == 0)
            {
                return;
            }

            var pageInventoryList = gadgetShopDataPages[currentPage];
            for (var i = 0; i < pageInventoryList.Count; i++)
            {
                var inventory = pageInventoryList[i];
                var view = gadgetInventoryViewPool.Get();
                view.Render(inventory, i * offset);
                view.OnClicked.Subscribe(HandleInventoryClicked).AddTo(gadgetInventoryViewCancellationToken.Token);
            }
        }

        private void InitializeInventoryPages(InventoryCategory category)
        {
            gadgetShopDataPages.Clear();

            var page = 0;
            for (var i = 0; i < gadgetShopDataList.Count; i++)
            {
                var data = gadgetShopDataList[i];
                var isLimit = gadgetShopDataPages.Count > 0 && gadgetShopDataPages[page].Count == itemsPerPage;

                if (data.category != category)
                {
                    continue;
                }

                if (!gadgetShopDataPages.TryGetValue(page, out var pageGadgetShopDataList) || isLimit)
                {
                    if (isLimit)
                    {
                        page++;
                    }

                    pageGadgetShopDataList = new List<GadgetShopData>();
                    gadgetShopDataPages.Add(page, pageGadgetShopDataList);
                }

                pageGadgetShopDataList.Add(data);
            }
        }

        private void HandleInventoryClicked(GadgetInventoryView gadgetInventoryView)
        {
            onClicked.OnNext(gadgetInventoryView);
        }
    }
}