using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core;
using Game.Core.Data;
using Modules.Core;
using TMPro;
using UnityEngine;

namespace Game.Views.BigShop
{
    public class InfoBundleItem : Actor
    {
        [SerializeField] private TMP_Text titleText;
        [SerializeField] private TMP_Text infoText;
        [SerializeField] private TMP_Text timerText;
        [SerializeField] private BuyBundleButton buyButton;

        private readonly DateTimer timer = new();
        private CancellationTokenSource disableCancellationTokenSource;

        public BuyBundleButton BuyButton => buyButton;

        private void OnEnable()
        {
            disableCancellationTokenSource = new CancellationTokenSource();
            timer.RemainingTime.Subscribe(HandleRemainingTime).AddTo(disableCancellationTokenSource.Token);
        }

        private void OnDisable()
        {
            disableCancellationTokenSource.CancelAndDispose();
        }

        public void Initialize(ShopItemData shopItem)
        {
            titleText.text = shopItem.title;
            infoText.text = shopItem.IsOwned ? "Owned" : shopItem.description;
            timerText.gameObject.SetActive(!shopItem.IsOwned);
            buyButton.SetPrice(shopItem.IsOwned ? "Equip" : shopItem.usdPriceText);

            if (!shopItem.IsOwned)
            {
                timer.Start(shopItem.startTime, shopItem.endTime);
            }
        }

        private void HandleRemainingTime(TimeSpan remainingTime)
        {
            timerText.text = $"{remainingTime.Days}d {remainingTime.Hours}h {remainingTime.Minutes}m";
        }
    }
}