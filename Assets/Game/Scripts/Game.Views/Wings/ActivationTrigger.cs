using System;
using System.Reactive.Subjects;
using Modules.XR;
using UnityEngine.XR.Interaction.Toolkit;

namespace Game.Views.Wings
{
    public class ActivationTrigger : XRSimpleSocketInteractor
    {
        private readonly ISubject<IXRInteractor> onHoverStarted = new Subject<IXRInteractor>();
        private readonly ISubject<IXRInteractor> onHoverEnded = new Subject<IXRInteractor>();
        private readonly ISubject<WingsActor> onTriggered = new Subject<WingsActor>();

        private bool isHover;

        public IObservable<IXRInteractor> OnHoverStarted => onHoverStarted;
        public IObservable<IXRInteractor> OnHoverEnded => onHoverEnded;
        public IObservable<WingsActor> OnTriggered => onTriggered;

        protected override void OnDisable()
        {
            base.OnDisable();
            isHover = false;
        }

        protected override void OnHoverEntered(HoverEnterEventArgs args)
        {
            base.OnHoverEntered(args);

            if (!TryGetWingsActor(args.interactableObject, out var actor) || !actor.HasStateAuthority)
            {
                return;
            }

            isHover = actor.IsGrabbed;

            if (isHover)
            {
                onHoverStarted.OnNext(actor.SelectInteractor);
            }
        }

        protected override void OnHoverExited(HoverExitEventArgs args)
        {
            base.OnHoverExited(args);

            if (!isHover)
            {
                return;
            }

            onHoverEnded.OnNext(args.interactorObject);
            isHover = false;
        }

        protected override void OnSelectEntered(SelectEnterEventArgs args)
        {
            base.OnSelectEntered(args);

            if (isHover && TryGetWingsActor(args.interactableObject, out var actor) && actor.HasStateAuthority)
            {
                onTriggered.OnNext(actor);
            }
        }

        public override bool CanSelect(IXRSelectInteractable interactable)
        {
            return base.CanSelect(interactable) && !interactable.isSelected && isHover;
        }

        public void SetActive(bool isActive)
        {
            gameObject.SetActive(isActive);
        }

        private static bool TryGetWingsActor(IXRInteractable interactable, out WingsActor actor)
        {
            return interactable.transform.TryGetComponent(out actor);
        }
    }
}