using UnityEngine;
using VContainer;
using VContainer.Unity;

namespace Game.Views.PlayerEffects
{
    public class PlayerEffectsManager : MonoBehaviour
    {
        [SerializeField] private SelfDamageEffectView selfDamageEffectViewPrefab;

        private IObjectResolver objectResolver;
        private SelfDamageEffectView selfDamageEffect;

        [Inject]
        private void Construct(IObjectResolver objectResolver)
        {
            this.objectResolver = objectResolver;
        }

        public void RenderSelfDamageEffect()
        {
            selfDamageEffect ??= objectResolver.Instantiate(selfDamageEffectViewPrefab, transform);
            selfDamageEffect.SetActive(true);
        }
    }
}