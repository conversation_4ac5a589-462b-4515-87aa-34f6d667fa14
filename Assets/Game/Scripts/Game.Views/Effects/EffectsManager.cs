using System.Collections.Generic;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.Effects
{
    public class EffectsManager : MonoBehaviour
    {
        private readonly Dictionary<EffectId, ComponentPool<EffectView>> effectPoolList = new();

        private EffectsConfig effectsConfig;
        private IObjectResolver objectResolver;

        [Inject]
        private void Construct(EffectsConfig effectsConfig, IObjectResolver objectResolver)
        {
            this.effectsConfig = effectsConfig;
            this.objectResolver = objectResolver;
        }

        public EffectView CreateEffect(EffectId id, Pose pose, float autoDestroyDelay = 0)
        {
            if (!TryGetEffect(id, out var effect))
            {
                return null;
            }

            effect.Initialize(pose, autoDestroyDelay);
            return effect;
        }

        public EffectView CreateEffect(EffectId id, Transform parent, float autoDestroyDelay = 0)
        {
            if (!TryGetEffect(id, out var effect))
            {
                return null;
            }

            effect.Initialize(parent, autoDestroyDelay);
            return effect;
        }

        public void DestroyEffect(EffectView effect)
        {
            if (effect == null || !effectPoolList.TryGetValue(effect.Id, out var pool))
            {
                return;
            }

            pool.Release(effect);
        }

        private bool TryGetEffect(EffectId id, out EffectView effect)
        {
            if (!effectsConfig.TryGetEffectViewPrefab(id, out var prefab))
            {
                effect = null;
                return false;
            }

            if (!effectPoolList.ContainsKey(id))
            {
                effectPoolList[id] = new ComponentPool<EffectView>(prefab, transform, objectResolver, 0);
            }

            effect = effectPoolList[id].Get();
            return true;
        }
    }
}