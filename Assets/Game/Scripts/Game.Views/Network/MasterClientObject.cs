using Fusion;
using Game.Core;
using Game.Core.Exceptions;
using Modules.Network;

namespace Game.Views.Network
{
    public class MasterClientObject : NetworkActor
    {
        [Networked] public int CreatorId { get; set; }

        public override void Spawned()
        {
            base.Spawned();

            if (HasStateAuthority)
            {
                var masterClientObjectCount = GetMasterClientObjectCount();
                if (masterClientObjectCount > 1)
                {
                    GameLogger.Network.Fatal(new MasterClientException("MasterClientObject already exists"));
                }
            }

            GameLogger.Network.Debug("MasterClientObject spawned");
        }

        public override void Despawned(NetworkRunner runner, bool hasState)
        {
            base.Despawned(runner, hasState);
            GameLogger.Network.Debug("MasterClientObject despawned");
        }

        private int GetMasterClientObjectCount()
        {
            if (Runner == null)
            {
                return 0;
            }

            var count = 0;
            var networkObjects = Runner.GetAllNetworkObjects();

            foreach (var networkObject in networkObjects)
            {
                if (networkObject != null && networkObject.TryGetComponent(out MasterClientObject _))
                {
                    count++;
                }
            }

            return count;
        }
    }
}