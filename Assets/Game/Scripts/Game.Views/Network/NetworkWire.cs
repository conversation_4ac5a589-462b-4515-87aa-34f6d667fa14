using System;
using System.Collections.Generic;
using System.Reactive;
using Cysharp.Threading.Tasks;
using Fusion;
using Fusion.Sockets;
using Modules.Network;
using UnityEngine;

namespace Game.Views.Network
{
    public class NetworkWire : NetworkWireBase
    {
        private readonly NetworkEventBus networkEventBus;
        private NetworkEventBusObject networkEventBusObject;

        public IReadOnlyAsyncReactiveProperty<bool> IsGameRunning => networkEventBus.isGameRunning;
        public IReadOnlyAsyncReactiveProperty<PlayerRef> MasterPlayer => networkEventBus.masterPlayer;
        public IObservable<Unit> OnMapSaved => networkEventBus.onMapSaved;
        public IObservable<bool> OnGameRunningUpdated => networkEventBus.onGameRunningUpdated;
        public IObservable<bool> OnBuildingDisabledUpdated => networkEventBus.onBuildingDisabledUpdated;
        public IObservable<Unit> OnFixedUpdateNetwork => networkEventBus.onFixedUpdateNetwork;
        public IObservable<PlayerRef> OnLevelFriendRequestReceived => networkEventBus.onLevelFriendRequestReceived;
        public IObservable<(PlayerRef player, bool ok)> OnLevelFriendResponseReceived => networkEventBus.onLevelFriendResponseReceived;
        public IObservable<(PlayerRef player, byte, Vector3)> OnEffectSpawnReceived => networkEventBus.onEffectSpawnReceived;
        public IObservable<(PlayerRef, string)> OnRaceEndReceived => networkEventBus.onRaceEndReceived;
        public IObservable<Unit> OnKillCurrentSessionReceived => networkEventBus.onKillCurrentSessionReceived;
        public IObservable<int> OnConsumeCoinsReceived => networkEventBus.onConsumeCoinsReceived;

        protected override Dictionary<Type, ReliableKey> PacketToReliableKey { get; } = new();

        public NetworkWire(INetworkClient networkClient, NetworkEventBus networkEventBus) : base(networkClient)
        {
            this.networkEventBus = networkEventBus;

            networkClient.OnNetworkActorSpawned.Subscribe(x => HandleNetworkActorSpawned(x.actor)).AddTo(DisposeCancellationTokenSource.Token);
            networkClient.OnNetworkActorDespawned.Subscribe(x => HandleNetworkActorDespawned(x.actor)).AddTo(DisposeCancellationTokenSource.Token);
        }

        public void SetIsGameRunning(bool isGameRunning)
        {
            if (!networkEventBusObject)
            {
                return;
            }

            networkEventBusObject.SetIsGameRunningRpc(isGameRunning);
        }

        public void SetIsBuildingDisabled(bool isBuildingDisabled)
        {
            if (!networkEventBusObject)
            {
                return;
            }

            networkEventBusObject.SetIsBuildingDisabledRpc(isBuildingDisabled);
        }

        public void SendMapSaved()
        {
            if (!networkEventBusObject)
            {
                return;
            }

            networkEventBusObject.SendMapSavedRpc();
        }

        public void SendLevelFriendRequest(PlayerRef playerRef)
        {
            if (!networkEventBusObject)
            {
                return;
            }

            networkEventBusObject.SendLevelFriendRequestRpc(playerRef);
        }

        public void SendLevelFriendResponse(PlayerRef playerRef, bool ok)
        {
            if (!networkEventBusObject)
            {
                return;
            }

            networkEventBusObject.SendLevelFriendResponseRpc(playerRef, ok);
        }

        protected override void ReceiveData(PlayerRef playerRef, ReliableKey reliableKey, byte[] data)
        {
        }

        private void HandleNetworkActorSpawned(NetworkActor networkActor)
        {
            if (networkActor is not NetworkEventBusObject currentRoomNetworkObject)
            {
                return;
            }

            networkEventBusObject = currentRoomNetworkObject;
        }

        private void HandleNetworkActorDespawned(NetworkActor networkActor)
        {
            if (networkActor is not NetworkEventBusObject)
            {
                return;
            }

            networkEventBusObject = null;
        }

        public void SendEffectSpawn(PlayerRef playerRef, byte code, Vector3 position)
        {
            if (!networkEventBusObject)
            {
                return;
            }

            networkEventBusObject.SendEffectSpawnRpc(playerRef, code, position);
        }

        public void SendRaceEnded(PlayerRef playerRef, string raceId)
        {
            if (!networkEventBusObject)
            {
                return;
            }

            networkEventBusObject.SendRaceEndedRpc(playerRef, raceId);
        }

        public void SendKillCurrentSession()
        {
            if (!networkEventBusObject)
            {
                return;
            }

            networkEventBusObject.SendKillCurrentSessionRpc();
        }

        public void SendConsumeCoins(PlayerRef playerRef, int coinAmount)
        {
            if (!networkEventBusObject)
            {
                return;
            }

            networkEventBusObject.SendConsumeCoinsRpc(playerRef, coinAmount);
        }
    }
}