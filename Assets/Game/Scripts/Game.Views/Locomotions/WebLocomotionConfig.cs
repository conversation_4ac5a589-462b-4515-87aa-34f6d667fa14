using Modules.Core;
using UnityEngine;

namespace Game.View.Locomotions
{
    public class WebLocomotionConfig : Config
    {
        [SerializeField] private float pullStrength = 1000;
        [SerializeField] private float spring = 4.5f;
        [SerializeField] private float damper = 7;
        [SerializeField] private float massScale = 4.5f;
        [SerializeField] private float maxGrappleStrength = 1.5f;

        public float PullStrength => pullStrength;
        public float Spring => spring;
        public float Damper => damper;
        public float MassScale => massScale;
        public float MaxGrappleStrength => maxGrappleStrength;
    }
}