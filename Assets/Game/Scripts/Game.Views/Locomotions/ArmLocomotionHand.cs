using System;
using System.Reactive.Subjects;
using UnityEngine;

namespace Game.View.Locomotions
{
    public class ArmLocomotionHand : MonoBehaviour
    {
        private readonly ISubject<ContactPoint> onTouched = new Subject<ContactPoint>();

        public IObservable<ContactPoint> OnTouched => onTouched;

        public Vector3 Position
        {
            get => transform.position;
            set => transform.position = value;
        }

        public void SetScale(float scale)
        {
            transform.localScale = scale * Vector3.one;
        }

        private void OnCollisionEnter(Collision other)
        {
            onTouched.OnNext(other.GetContact(0));
        }
    }
}