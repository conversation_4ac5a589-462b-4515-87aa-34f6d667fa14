using Game.Views.Atm;
using Game.Views.BigShop;
using Game.Views.Blocks;
using Game.Views.BlockShop;
using Game.Views.BuildIslands;
using Game.Views.Effects;
using Game.Views.GadgetShop;
using Game.Views.Levels;
using Game.Views.Lobby;
using Game.Views.Ores;
using Game.Views.Shared;
using Game.Views.SmallShop;
using Game.Views.World;
using Modules.Core;
using UnityEngine;
using VContainer;
using VContainer.Unity;

namespace Game.Views
{
    public class ViewsMonoInstaller : MonoInstaller
    {
        [SerializeField] private EffectsConfig effectsConfig;

        public override void Install(IContainerBuilder builder, Transform node = null)
        {
            builder.RegisterInstance(effectsConfig);

            builder.RegisterComponentInHierarchy<LevelSpaceManager>();
            builder.RegisterComponentInHierarchy<LobbySpaceManager>();
            builder.RegisterComponentInHierarchy<WorldBoundary>();
            builder.RegisterComponentInHierarchy<VideoRecorder>();
            builder.RegisterComponentInHierarchy<EffectsManager>();
            builder.RegisterComponentInHierarchy<BigShopManager>();
            builder.RegisterComponentInHierarchy<SmallShopManager>();
            builder.RegisterComponentInHierarchy<AtmManager>();
            builder.RegisterComponentInHierarchy<BuildIslandsManager>();
            builder.RegisterComponentInHierarchy<BlocksManager>();
            builder.RegisterComponentInHierarchy<GadgetShopManager>();
            builder.RegisterComponentInHierarchy<BlockShopManager>();
            builder.RegisterComponentInHierarchy<OreConverterManager>();

            builder.Register<DistanceCuller>(Lifetime.Singleton);
            builder.Register<BuildZoneHandler>(Lifetime.Singleton);
        }
    }
}