using Fusion;
using UnityEngine;

namespace Game.Views.Players
{
    [RequireComponent(typeof(NetworkTransform))]
    [DefaultExecutionOrder(ExecutionOrder)]
    public class NetworkHead : NetworkBehaviour
    {
        private const int ExecutionOrder = NetworkRig.ExecutionOrder + 10;

        public void SetPositionAndRotation(Vector3 position, Quaternion rotation)
        {
            transform.SetPositionAndRotation(position, rotation);
        }
    }
}