using Fusion;
using Modules.Core;
using UnityEngine;

namespace Game.Views.Players
{
    [RequireComponent(typeof(NetworkTransform))]
    [DefaultExecutionOrder(ExecutionOrder)]
    public class NetworkHand : NetworkBehaviour
    {
        public const int ExecutionOrder = NetworkRig.ExecutionOrder + 10;

        [SerializeField] private HandType handType;
        [SerializeField] private Transform attachNode;

        public HandType HandType => handType;
        public Transform AttachNode => attachNode;

        public void SetPositionAndRotation(Vector3 position, Quaternion rotation)
        {
            transform.SetPositionAndRotation(position, rotation);
        }
    }
}