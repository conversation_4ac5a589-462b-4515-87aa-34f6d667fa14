using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.Players
{
    public class PlayersMonoInstaller : MonoInstaller
    {
        [SerializeField] private PlayersConfig playersConfig;

        public override void Install(IContainerBuilder builder, Transform node = null)
        {
            builder.Register<PlayersModel>(Lifetime.Singleton);
            builder.RegisterInstance(playersConfig);
        }
    }
}