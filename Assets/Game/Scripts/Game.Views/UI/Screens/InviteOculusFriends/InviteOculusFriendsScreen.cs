using System;
using System.Reactive;
using Modules.UI;
using TMPro;
using UnityEngine;

namespace Game.Views.UI.Screens.Challenges
{
    public class InviteOculusFriendsScreen : GameScreen
    {
        [SerializeField] private XRButtonWidget friendInviteButton;
        [SerializeField] private TMP_Text remzBalance;
        [SerializeField] private TMP_Text progress;
        [SerializeField] private LoadingPanel loadingPanel;
        [SerializeField] private GameObject mainPanel;

        public IObservable<Unit> OnFriendInviteButtonClicked => friendInviteButton.OnClicked;

        public void SetData(string remzBalance, int friendInviteLimit, int friendsInvitedToday)
        {
            this.remzBalance.text = remzBalance;
            friendInviteButton.SetInteractable(friendsInvitedToday < friendInviteLimit);
            progress.text = $"{friendsInvitedToday}/{friendInviteLimit} friends invited today";
        }

        public void SetActiveLoadingState(bool isActive)
        {
            loadingPanel.SetActive(isActive);
            mainPanel.SetActive(!isActive);
            friendInviteButton.SetActive(!isActive);
        }
    }
}