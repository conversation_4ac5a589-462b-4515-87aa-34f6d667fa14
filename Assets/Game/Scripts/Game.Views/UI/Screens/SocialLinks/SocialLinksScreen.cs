using System;
using System.Reactive;
using Modules.UI;
using UnityEngine;

namespace Game.Views.UI.Screens.Challenges
{
    public class SocialLinksScreen : GameScreen
    {
        [SerializeField] private XRButtonWidget joinYoutubeButton;
        [SerializeField] private XRButtonWidget joinDiscordButton;
        [SerializeField] private XRButtonWidget joinTiktokButton;

        public IObservable<Unit> OnJoinYoutubeButtonClicked => joinYoutubeButton.OnClicked;
        public IObservable<Unit> OnJoinDiscordButtonClicked => joinDiscordButton.OnClicked;
        public IObservable<Unit> OnJoinTiktokButtonClicked => joinTiktokButton.OnClicked;
    }
}