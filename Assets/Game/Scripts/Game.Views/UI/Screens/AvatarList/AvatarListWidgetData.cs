using UnityEngine;

namespace Game.Views.UI
{
    public class AvatarListWidgetData
    {
        public readonly string ItemId;
        public readonly string ItemName;
        public readonly Sprite ItemTexture;

        public AvatarListWidgetData(string itemId, string itemName, Sprite itemTexture)
        {
            ItemId = itemId;
            ItemName = itemName;
            ItemTexture = itemTexture;
        }
    }
}