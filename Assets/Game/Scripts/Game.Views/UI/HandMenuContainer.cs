using System;
using Modules.UI;
using UnityEngine;

namespace Game.Views.UI
{
    public class HandMenuContainer : HandMenuContainerBase
    {
        [SerializeField] private Transform headNode;
        [SerializeField] private HandSettings leftHand;
        [SerializeField] private HandSettings rightHand;

        public override void Assign(HandMenuScreen screen)
        {
            if (screen.ScreenSettings.HandMenuType == HandMenuType.None)
            {
                return;
            }

            var hand = screen.ScreenSettings.HandMenuType == HandMenuType.Left ? leftHand : rightHand;
            screen.SetHandMenuContainer(this);
            hand.Assign(screen);
        }

        public override void UpdatePositionAndRotation()
        {
            leftHand.UpdatePositionAndRotation();
            rightHand.UpdatePositionAndRotation();
        }

        private void Update()
        {
            leftHand.UpdateVisibility(headNode);
            rightHand.UpdateVisibility(headNode);
        }

        [Serializable]
        private class HandSettings
        {
            [SerializeField] private Transform parent;

            private HandMenuScreen screen;
            private HandMenuScreen.Settings settings;

            public void Assign(HandMenuScreen screen)
            {
                this.screen = screen;
                settings = screen.ScreenSettings;
                screen.transform.SetParent(parent, false);
                screen.transform.localScale = Vector3.one;
                UpdatePositionAndRotation();
            }

            public void UpdatePositionAndRotation()
            {
                if (screen == null)
                {
                    return;
                }

                screen.transform.SetLocalPositionAndRotation(screen.Scale * settings.Position, Quaternion.Euler(settings.Rotation));
            }

            public void UpdateVisibility(Transform headNode)
            {
                if (screen == null || !screen.UpdateVisibilityEnabled)
                {
                    return;
                }

                screen.UpdateVisibility(headNode);
            }
        }
    }
}