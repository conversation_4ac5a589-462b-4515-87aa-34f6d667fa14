using System.Collections.Generic;
using Game.Core.Data;
using Game.Views.InteractablesCore;

namespace Game.Views.Badges
{
    public class BadgesManager : InteractablesManagerBase<BadgeData, BadgesConfig, BadgeView>
    {
        public List<ShopInventoryData> GetBadgeInventoryList(List<UserLabel> userLabelList)
        {
            var result = new List<ShopInventoryData>();
            foreach (var userLabel in userLabelList)
            {
                var data = Config.GetBadgeData(userLabel);
                if (data == null)
                {
                    continue;
                }

                result.Add(new ShopInventoryData
                {
                    id = data.Code,
                    title = data.Title,
                    viewCode = data.Code,
                    category = InventoryCategory.Badge,
                    isPurchased = true
                });
            }

            return result;
        }
    }
}