using Game.Views.UI;
using JetBrains.Annotations;
using Modules.Core;
using Modules.XR;
using VContainer.Unity;

namespace Game.Main
{
    internal class NoLazyInjector : IStartable
    {
        [UsedImplicitly]
        private NoLazyInjector(SharedScreensContainer sharedScreensContainer, IXRSystem xrSystem, AppUtilityProvider appUtilityProvider)
        {
            xrSystem.SetXRInteractionManager();
        }

        void IStartable.Start()
        {
        }
    }
}