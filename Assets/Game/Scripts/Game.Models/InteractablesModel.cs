using System;
using System.Reactive.Subjects;
using Modules.Core;
using UnityEngine.XR.Interaction.Toolkit;

namespace Game.Models
{
    public class InteractablesModel : ModelBase
    {
        private readonly ISubject<CreateInteractableArgs> onInteractableCreating = new Subject<CreateInteractableArgs>();

        public IObservable<CreateInteractableArgs> OnInteractableCreating => onInteractableCreating;

        public void CreateInteractable(string code, IXRSelectInteractor interactor, bool playSound = false)
        {
            onInteractableCreating.OnNext(new CreateInteractableArgs(code, interactor, playSound));
        }
    }
}