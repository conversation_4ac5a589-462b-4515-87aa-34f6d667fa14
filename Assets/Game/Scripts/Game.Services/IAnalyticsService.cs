namespace Game.Services
{
    public interface IAnalyticsService
    {
        void SetupUser(string userId, string userName);
        void StartGame(string levelName, string roomId, int playerCount, int gameCount, int fps);
        void EndGame(string levelName, int playerCount, int localtime, string avatar, int fps);
        void Purchase(string title, string category, float usdSpent, int coinSpent, int coinReceived, string source);
        void BufferShouldBeNullReceived(string levelName, int playerId, int localTime, int serverTime);
        void Initialize();
    }
}