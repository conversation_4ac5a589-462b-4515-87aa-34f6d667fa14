using System.Linq;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Services
{
    internal class ServicesMonoInstaller : MonoInstaller
    {
        [SerializeField] private DebugConfig debugConfig;
        [SerializeField] private LevelServiceConfig levelServiceConfig;
        [SerializeField] private GameLogsWebhookData gameLogsWebhookData;
        [SerializeField] private ModerationWebhookData moderationWebhookData;

        public override void Install(IContainerBuilder builder, Transform node = null)
        {
            if (debugConfig.UseCloudMock)
            {
                builder.Register<IEconomyService, EconomyServiceMock>(Lifetime.Singleton);
            }
            else
            {
                builder.Register<IEconomyService, EconomyService>(Lifetime.Singleton);
            }

            builder.RegisterInstance(levelServiceConfig);
            builder.RegisterInstance(gameLogsWebhookData);
            builder.RegisterInstance(moderationWebhookData);
            builder.Register<UriProvider>(Lifetime.Singleton);
            builder.Register<IGameService, GameService>(Lifetime.Singleton);
            builder.Register<ILevelService, LevelService>(Lifetime.Singleton);
            builder.Register<IAnalyticsService, AnalyticsService>(Lifetime.Singleton);
            builder.Register<ILeaderboardService, LeaderboardService>(Lifetime.Singleton);
            builder.Register<IBuildIslandService, BuildIslandService>(Lifetime.Singleton);
            builder.Register<IRemoteConfigService, RemoteConfigService>(Lifetime.Singleton);
            builder.Register<INotificationService, NotificationService>(Lifetime.Singleton);
            builder.Register<IFirebaseTokenService, FirebaseTokenService>(Lifetime.Singleton);
            builder.Register<IInitializeGameService, InitializeGameService>(Lifetime.Singleton);
            builder.Register<IBlockInventoryService, BlockInventoryService>(Lifetime.Singleton);

            RegisterServicesMockConfig(builder);
        }

        private void RegisterServicesMockConfig(IContainerBuilder builder)
        {
#if UNITY_EDITOR
            var servicesMockConfig = UnityEditor.AssetDatabase
                .FindAssets($"t:{nameof(ServicesMockConfig)}")
                .Select(UnityEditor.AssetDatabase.GUIDToAssetPath)
                .Select(UnityEditor.AssetDatabase.LoadAssetAtPath<ServicesMockConfig>)
                .FirstOrDefault();

            if (servicesMockConfig == null)
            {
                builder.Register<IServicesMockConfig, NullServicesMockConfig>(Lifetime.Singleton);
            }
            else
            {
                builder.RegisterInstance(servicesMockConfig).AsImplementedInterfaces();
            }
#else
            builder.Register<IServicesMockConfig, NullServicesMockConfig>(Lifetime.Singleton);
#endif
        }
    }
}