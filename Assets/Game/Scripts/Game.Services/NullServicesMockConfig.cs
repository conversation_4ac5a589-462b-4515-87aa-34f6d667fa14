using System.Collections.Generic;
using Game.Core.Data;

namespace Game.Services
{
    internal class NullServicesMockConfig : IServicesMockConfig
    {
        public List<ShopItemData> ShopItemList { get; } = new();
        public List<ShopInventoryData> InventoryList { get; } = new();

        public void SetEconomyData(List<ShopItemData> shopItemList, List<ShopInventoryData> inventoryList)
        {
        }
    }
}