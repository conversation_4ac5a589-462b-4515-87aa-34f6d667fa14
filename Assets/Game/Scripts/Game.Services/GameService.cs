using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using Game.Core;
using Game.Core.Data;
using Game.Services.Data;
using Modules.Core;
using Modules.Oculus;
using Modules.UnityGameServices;
using Error = Modules.Core.Error;

namespace Game.Services
{
    internal class GameService : IGameService
    {
        private readonly IOculusClient oculusClient;
        private readonly IUnityGameServices unityGameServices;
        private readonly IAsyncReactiveProperty<int> xpAmount = new AsyncReactiveProperty<int>(0);

        public IReadOnlyAsyncReactiveProperty<int> XpAmount => xpAmount;

        public GameService(IOculusClient oculusClient, IUnityGameServices unityGameServices)
        {
            this.oculusClient = oculusClient;
            this.unityGameServices = unityGameServices;
        }

        public async UniTask<InitialGameDataContract> Initialize(CancellationToken cancellationToken)
        {
            var oculusNameResult = await GetO<PERSON><PERSON>ame(cancellationToken);
            await UpdateOculusName(oculusNameResult.Value, cancellationToken);
            return oculusNameResult.IsOk ? await GetInitialGameData(cancellationToken) : GetShortInitialGameData();
        }

        public async UniTask<DayStreakContract> GetDayStreak(CancellationToken cancellationToken)
        {
            var keys = new HashSet<string>
            {
                UserDataKeys.LastEntryTime,
                UserDataKeys.EntryStreakCount
            };
            var contract = new DayStreakContract();
            var result = await unityGameServices.GetUserData(keys, cancellationToken);

            if (result.IsFail)
            {
                return contract;
            }

            var data = result.Value;

            if (data.TryGetValue(UserDataKeys.LastEntryTime, out string lastEntryTime))
            {
                contract.lastEntryTime = lastEntryTime;
            }

            if (data.TryGetValue(UserDataKeys.EntryStreakCount, out int entryStreakCount))
            {
                contract.entryStreakCount = entryStreakCount;
            }

            return contract;
        }

        public async UniTask SetDayStreak(DayStreakContract contract, CancellationToken cancellationToken)
        {
            var data = new Dictionary<string, object>
            {
                { UserDataKeys.LastEntryTime, contract.lastEntryTime },
                { UserDataKeys.EntryStreakCount, contract.entryStreakCount }
            };
            await unityGameServices.SetUserData(data, cancellationToken);
        }

        public void SetGiftClaimTime(string dateTime, CancellationToken cancellationToken)
        {
            var data = new Dictionary<string, object> { { UserDataKeys.GiftClaimTime, dateTime } };
            unityGameServices.SetUserData(data, cancellationToken).Forget();
        }

        public void SetPrisonEndTime(string dateTime, CancellationToken cancellationToken)
        {
            var data = new Dictionary<string, object> { { UserDataKeys.PrisonTimeEnd, dateTime } };
            unityGameServices.SetUserData(data, cancellationToken).Forget();
        }

        public void SetMuteEndTime(string dateTime, CancellationToken cancellationToken)
        {
            var data = new Dictionary<string, object> { { UserDataKeys.MuteTimeEnd, dateTime } };
            unityGameServices.SetUserData(data, cancellationToken).Forget();
        }

        public async UniTask<Result> UpdateXpAmount(CancellationToken cancellationToken)
        {
            var result = await unityGameServices.GetBalance(Constants.XpId, cancellationToken);
            if (result.IsOk)
            {
                xpAmount.Value = result.Value;
            }

            return result;
        }

        public async UniTask<Result> AddXpAmount(int xpAmount, CancellationToken cancellationToken)
        {
            var result = await unityGameServices.IncrementBalance(Constants.XpId, xpAmount, cancellationToken);
            if (result.IsOk)
            {
                this.xpAmount.Value = result.Value;
            }

            return result;
        }

        private async UniTask<InitialGameDataContract> GetInitialGameData(CancellationToken cancellationToken)
        {
            var keys = new HashSet<string>
            {
                UserDataKeys.GiftClaimTime,
                UserDataKeys.LastEntryTime,
                UserDataKeys.EntryStreakCount,
                UserDataKeys.UserLabels,
                UserDataKeys.PrisonTimeEnd,
                UserDataKeys.MuteTimeEnd
            };

            var result = await unityGameServices.GetUserData(keys, cancellationToken);
            var contract = GetShortInitialGameData();

            if (result.IsFail)
            {
                return contract;
            }

            var data = result.Value;

            if (data.TryGetValue(UserDataKeys.GiftClaimTime, out string giftClaimTime))
            {
                contract.giftClaimTime = giftClaimTime;
            }

            if (data.TryGetValue(UserDataKeys.UserLabels, out UserLabel[] userLabels))
            {
                contract.userLabels = userLabels;
            }

            if (data.TryGetValue(UserDataKeys.PrisonTimeEnd, out string prisonTimeEnd))
            {
                contract.prisonTimeEnd = prisonTimeEnd;
            }

            if (data.TryGetValue(UserDataKeys.MuteTimeEnd, out string muteTimeEnd))
            {
                contract.muteTimeEnd = muteTimeEnd;
            }

            return contract;
        }

        private async UniTask<Result<string>> GetOculusName(CancellationToken cancellationToken)
        {
            var result = await unityGameServices.GetUserData(new HashSet<string> { UserDataKeys.OculusName }, cancellationToken);
            if (result.IsFail || result.Value == null || !result.Value.TryGetValue<string>(UserDataKeys.OculusName, out var oculusName))
            {
                return Result<string>.Fail(new Error("GetOculusName", "Failed to retrieve oculus name"));
            }

            return Result<string>.Ok(oculusName);
        }

        private async UniTask UpdateOculusName(string previousOculusName, CancellationToken cancellationToken)
        {
            var currentOculusName = oculusClient.UserName.ToLower();
            if (!string.IsNullOrEmpty(previousOculusName) && previousOculusName == currentOculusName)
            {
                return;
            }

            var data = new Dictionary<string, object>
            {
                { UserDataKeys.OculusName, currentOculusName }
            };
            await unityGameServices.SetUserData(data, cancellationToken);
        }

        private InitialGameDataContract GetShortInitialGameData()
        {
            return new InitialGameDataContract
            {
                userId = unityGameServices.UserId,
                userName = oculusClient.UserName,
                userLabels = Array.Empty<UserLabel>()
            };
        }
    }
}