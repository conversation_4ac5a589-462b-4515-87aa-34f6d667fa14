using System;
using Cysharp.Threading.Tasks;
using Game.Views.InteractablesCore;
using Game.Views.Weapons;
using Modules.Core;
using Modules.XR;
using UnityEngine.InputSystem;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;

namespace Game.Controllers.Weapons
{
    public class HammerController : ControllerBase
    {
        private HammerActor lastHammerActor;
        private IXRInput xrInput;

        [Inject]
        private void Construct(IInteractableInteractionSubscriber interactableInteractionsSubscriber, IXRInput xrInput)
        {
            this.xrInput = xrInput;

            interactableInteractionsSubscriber.OnSelectExited.Subscribe(HandleSelectExited).AddTo(DisposeCancellationToken);

            xrInput.OnLeftSelect.Subscribe(HandleLeftActivate).AddTo(DisposeCancellationToken);
            xrInput.OnRightSelect.Subscribe(HandleRightActivate).AddTo(DisposeCancellationToken);
        }

        private void HandleSelectExited(InteractionArgs<SelectExitEventArgs> args)
        {
            if (args.interactable is not HammerActor hammerActor)
            {
                return;
            }

            lastHammerActor = hammerActor;
        }

        private void HandleLeftActivate(InputAction.CallbackContext obj)
        {
            if (lastHammerActor != null && lastHammerActor.HasStateAuthority && !xrInput.LeftDirectInteractor.hasHover && !xrInput.LeftDirectInteractor.hasSelection)
            {
                lastHammerActor.Grab(xrInput.LeftDirectInteractor);
            }
        }

        private void HandleRightActivate(InputAction.CallbackContext obj)
        {
            if (lastHammerActor != null && lastHammerActor.HasStateAuthority && !xrInput.RightDirectInteractor.hasHover && !xrInput.RightDirectInteractor.hasSelection)
            {
                lastHammerActor.Grab(xrInput.RightDirectInteractor);
            }
        }
    }
}