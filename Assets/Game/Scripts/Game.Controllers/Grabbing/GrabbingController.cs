using Cysharp.Threading.Tasks;
using Game.Views.Grabbing;
using Game.Views.Players;
using MessagePipe;
using Modules.Core;
using VContainer;

namespace Game.Controllers.Grabbing
{
    public class GrabbingController : ControllerBase
    {
        private PlayersModel playersModel;

        private PlayerActor LocalPlayer => playersModel.LocalPlayer.Value;

        [Inject]
        private void Construct(ISubscriber<GrabbableItemGrabArgs> grabSubscriber, PlayersModel playersModel)
        {
            this.playersModel = playersModel;

            grabSubscriber.Subscribe(HandleGrab).AddTo(DisposeCancellationToken);
        }

        private void HandleGrab(GrabbableItemGrabArgs args)
        {
            if (LocalPlayer == null)
            {
                return;
            }

            args.grabbableItem.GrabberId = args.isLeftHand ? LocalPlayer.LeftNetworkHand.Id : LocalPlayer.RightNetworkHand.Id;
        }
    }
}