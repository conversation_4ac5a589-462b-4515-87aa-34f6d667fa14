using System;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core;
using Game.Models;
using Game.Views.UI.Screens.Menu;
using Modules.Core;
using Modules.Network;
using Modules.UI;
using Modules.XR;
using UnityEngine;
using VContainer;

namespace Game.Controllers.VideoRecorder
{
    public class VideoRecorderController : ControllerBase
    {
        private IXRPlayer xrPlayer;
        private GameConfig gameConfig;
        private INetworkClient networkClient;
        private Views.Shared.VideoRecorder videoRecorder;

        [Inject]
        private void Construct(GameModel gameModel, GameConfig gameConfig, Views.Shared.VideoRecorder videoRecorder, INetworkClient networkClient, IXRPlayer xrPlayer, IScreenManager screenManager)
        {
            this.networkClient = networkClient;
            this.xrPlayer = xrPlayer;
            this.gameConfig = gameConfig;
            this.videoRecorder = videoRecorder;

            var menuPanel = screenManager.GetScreen<MenuScreen>().MenuPanel;
            menuPanel.OnOpenCamera.Subscribe(_ => HandleMenuButtonClicked(true)).AddTo(DisposeCancellationToken);
            menuPanel.OnCloseCamera.Subscribe(_ => HandleMenuButtonClicked(false)).AddTo(DisposeCancellationToken);

            networkClient.OnShutdown.Subscribe(_ => HandleShutdown()).AddTo(DisposeCancellationToken);
            gameModel.IsInitialized.Where(ok => ok).Subscribe(_ => HandleGameInitialized()).AddTo(DisposeCancellationToken);
        }

        private void HandleGameInitialized()
        {
            videoRecorder.SetEnabled(gameConfig.VideoRecorderEnabled);
        }

        private void HandleShutdown()
        {
            videoRecorder.HideTablet();
        }

        private void HandleMenuButtonClicked(bool open)
        {
            if (!videoRecorder.IsEnabled || !networkClient.IsConnected.Value)
            {
                return;
            }

            if (!open)
            {
                videoRecorder.HideTablet();
                return;
            }

            var headNode = xrPlayer.HeadNode;
            var position = headNode.position + 0.8f * headNode.forward.SetY(0) + 0.25f * Vector3.down;
            var rotation = Quaternion.LookRotation(-headNode.forward.SetY(0));
            videoRecorder.RenderTablet(new Pose(position, rotation));
        }
    }
}