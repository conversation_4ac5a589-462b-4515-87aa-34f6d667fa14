using System.Threading;
using Cysharp.Threading.Tasks;
using Game.Core;
using Game.Views.Scenes;
using Modules.Core;
using Modules.Network;
using Modules.XR;
using VContainer;

namespace Game.Controllers.States
{
    public class RestartState : StateBase
    {
        private IXRPlayer xrPlayer;
        private INetworkClient networkClient;
        private IScenesManager scenesManager;

        [Inject]
        private void Construct(INetworkClient networkClient, IScenesManager scenesManager, IXRPlayer xrPlayer)
        {
            this.xrPlayer = xrPlayer;
            this.networkClient = networkClient;
            this.scenesManager = scenesManager;
        }

        public override async UniTask EnterAsync(CancellationToken cancellationToken = default)
        {
            await networkClient.Disconnect(cancellationToken: cancellationToken);
            await UniTask.Delay(Constants.ConnectionRetryInterval, cancellationToken: cancellationToken);
            await xrPlayer.FadeInViewAsync();
            await scenesManager.LoadLobby();
        }
    }
}