using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core;
using Game.Models;
using Game.Views.Players;
using Game.Views.Voxels;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Controllers.Voxels
{
    public class ReceiveInitialVoxelsController : ControllerBase
    {
        private const float ReceivingInitialVoxelsTimeout = 5;

        private int initialVoxelCount;
        private float lastInitialVoxelTime;
        private PlayersModel playersModel;
        private VoxelSpaceManager voxelSpaceManager;
        private CancellationTokenSource receivingInitialVoxelsCancellationTokenSource;

        private PlayerActor LocalPlayer => playersModel.LocalPlayer.Value;

        [Inject]
        private void Construct(PlayersModel playersModel, VoxelModel voxelModel, VoxelSpaceManager voxelSpaceManager)
        {
            this.voxelSpaceManager = voxelSpaceManager;
            this.playersModel = playersModel;

            voxelModel.OnSliceMapReceived.Subscribe(_ => StartReceiveInitialVoxels()).AddTo(DisposeCancellationToken);
            voxelSpaceManager.OnInitialVoxelCreating.Subscribe(_ => UpdateLastInitialVoxelTime()).AddTo(DisposeCancellationToken);
            voxelSpaceManager.OnInitialVoxelDestroying.Subscribe(_ => UpdateLastInitialVoxelTime()).AddTo(DisposeCancellationToken);
            voxelSpaceManager.OnInitialVoxelsCompleting.Subscribe(_ => CompleteInitialVoxels()).AddTo(DisposeCancellationToken);
        }

        public override void Dispose()
        {
            base.Dispose();
            receivingInitialVoxelsCancellationTokenSource.CancelAndDispose();
        }

        private void StartReceiveInitialVoxels()
        {
            receivingInitialVoxelsCancellationTokenSource.CancelAndDispose();

            if (LocalPlayer == null)
            {
                return;
            }

            initialVoxelCount = 0;
            lastInitialVoxelTime = 0;
            receivingInitialVoxelsCancellationTokenSource = new CancellationTokenSource();
            var cancellationToken = CancellationTokenSource.CreateLinkedTokenSource(receivingInitialVoxelsCancellationTokenSource.Token, LocalPlayer.destroyCancellationToken).Token;
            UniTaskAsyncEnumerable.EveryUpdate().Subscribe(_ => ReceivingInitialVoxels()).AddTo(cancellationToken);
        }

        private void ReceivingInitialVoxels()
        {
            if (Time.time - lastInitialVoxelTime < ReceivingInitialVoxelsTimeout)
            {
                return;
            }

            if (TryGetInitialVoxelsSender(out var sender))
            {
                GameLogger.Voxels.Debug("Try get initial voxels from sender {0}", sender.StateAuthority.PlayerId);
                voxelSpaceManager.ReceivingInitialVoxels(sender.StateAuthority);
                lastInitialVoxelTime = Time.time;
            }
            else
            {
                CompleteInitialVoxels();
            }
        }

        private void UpdateLastInitialVoxelTime()
        {
            initialVoxelCount++;
            lastInitialVoxelTime = Time.time;
        }

        private void CompleteInitialVoxels()
        {
            receivingInitialVoxelsCancellationTokenSource.CancelAndDispose();

            if (LocalPlayer != null)
            {
                GameLogger.Voxels.Debug("Received all initial voxels. Initial voxel count: {0}", initialVoxelCount);
                LocalPlayer.SetInitialVoxelsReceived(true);
            }
        }

        private bool TryGetInitialVoxelsSender(out PlayerActor player)
        {
            var players = new List<PlayerActor>();
            foreach (var currentPlayer in playersModel.Players)
            {
                if (currentPlayer.HasStateAuthority || !currentPlayer.IsInitialVoxelsReceived.Value)
                {
                    continue;
                }

                players.Add(currentPlayer);
            }

            player = players.Count > 0 ? players.RandomItem() : null;
            return player != null;
        }
    }
}