{"name": "Game.Controllers", "rootNamespace": "", "references": ["Modules.Core", "Modules.UI", "Modules.XR", "Modules.Oculus", "Modules.Network", "Modules.Analytics", "Modules.CloudRequest", "Modules.UnityGameServices", "Game.Core", "Game.Models", "Game.Views", "Game.Services", "Unity.InputSystem", "Unity.TextMeshPro", "Unity.XR.Interaction.Toolkit", "VoxelPlay", "MessagePipe", "MessagePipe.VContainer", "<PERSON><PERSON><PERSON>", "UniTask", "UniTask.Linq"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": true, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}