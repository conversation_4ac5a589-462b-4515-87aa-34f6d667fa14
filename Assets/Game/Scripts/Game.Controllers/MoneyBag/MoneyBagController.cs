using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core;
using Game.Models;
using Game.Views.Items;
using Game.Views.Levels;
using Game.Views.Players;
using Game.Views.PlayerUI;
using Game.Views.UI.Screens.Modal;
using Game.Views.UI.Screens.Notification;
using Modules.Core;
using Modules.UI;
using Modules.XR;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;

namespace Game.Controllers.MoneyBag
{
    public class MoneyBagController : ControllerBase
    {
        private GameConfig config;
        private PlayerMenu playerMenu;
        private IAudioClient audioClient;
        private EconomyModel economyModel;
        private PlayersModel playersModel;
        private ItemsManager itemsManager;
        private ModalScreen modalScreen;
        private NotificationScreen notificationScreen;
        private LevelModel levelModel;

        private bool leftHandHoldingMoneyBag, rightHandHoldingMoneyBag;
        private bool IsHoldingMoneyBag => leftHandHoldingMoneyBag || rightHandHoldingMoneyBag;
        private bool isPlayerWarned;
        
        private CancellationTokenSource levelLoadedCancellationTokenSource;

        [Inject]
        private void Construct(
            PlayerMenu playerMenu,
            IXRInput xrInput,
            ItemsManager itemsManager,
            IAudioClient audioClient,
            GameConfig config,
            EconomyModel economyModel,
            IScreenManager screenManager,
            LevelModel levelModel)
        {
            this.config = config;
            this.playerMenu = playerMenu;
            this.audioClient = audioClient;
            this.itemsManager = itemsManager;
            this.economyModel = economyModel;
            this.levelModel = levelModel;
            
            modalScreen = screenManager.GetScreen<ModalScreen>(true);
            notificationScreen = screenManager.GetScreen<NotificationScreen>();

            economyModel.CoinAmount.Subscribe(HandleBalanceChanged).AddTo(DisposeCancellationToken);
            playerMenu.MoneyBagCollectMenu.OnCollected.Subscribe(x => HandleCollectedAsync(x).Forget()).AddTo(DisposeCancellationToken);
            
            playerMenu.MoneyBagCollectMenu.SetMoneyBagCollectionActive(false);
            xrInput.OnGrab.Subscribe(HandleMoneyBagGrabbed).AddTo(DisposeCancellationToken);
            xrInput.OnUngrab.Subscribe(HandleMoneyBagUnGrabbed).AddTo(DisposeCancellationToken);
            
            levelModel.OnLevelLoaded.Subscribe(HandleLevelLoaded).AddTo(DisposeCancellationToken);
            
        }

        public override void Dispose()
        {
            base.Dispose();
            levelLoadedCancellationTokenSource.CancelAndDispose();
        }

        private void HandleLevelLoaded(bool loaded)
        {
            levelLoadedCancellationTokenSource.CancelAndDispose();

            if (loaded && !levelModel.LevelConfig.DisableMoneyBagSpawner)
            {
                levelLoadedCancellationTokenSource = new CancellationTokenSource();
                playerMenu.HandsMenu.OnMoneyBagMenuClicked.Subscribe(HandleMoneyBagMenuClicked).AddTo(levelLoadedCancellationTokenSource.Token);
                playerMenu.HandsMenu.SetMoneyBagActive(true);
            }
            else
            {
                playerMenu.HandsMenu.SetMoneyBagActive(false);
            }
        }

        private void HandleBalanceChanged(int balance)
        {
            playerMenu.HandsMenu.SetBalanceText(balance);
        }

        private async UniTaskVoid HandleCollectedAsync(MoneyBagActor actor)
        {
            audioClient.Play(AudioKeys.MoneyBagCollect, actor.transform.position, DisposeCancellationToken);
            itemsManager.DestroyActor(actor);
            await economyModel.AddCoinAmount(config.MoneyBagAmount, DisposeCancellationToken);
            notificationScreen.Show($"{config.MoneyBagAmount} collected! \n\nYou now have {economyModel.CoinAmount.Value} {Constants.CoinName}", 5);
        }

        private void HandleMoneyBagMenuClicked(InteractableWidget widget)
        {
            if (!isPlayerWarned)
            {
                modalScreen.Render(
                        "Be careful of scammers!\n\nIf you drop this bag someone can steal it.\n To collect coins, put it back in your pouch.\n Continue?",
                        "Yes",
                        "No",
                        () =>
                        {
                            isPlayerWarned = true;
                            modalScreen.Hide();
                        },
                        () => { modalScreen.Hide(); })
                    .WithTimeout(20, modalScreen.Hide);
                return;
            }

            if (economyModel.CoinAmount.Value >= config.MoneyBagAmount)
            {
                playerMenu.HandsMenu.SpawnMoneyBag(widget);
                HandleMoneyBagSpawned().Forget();
            }
            else
            {
                notificationScreen.Show($"Not enough {Constants.CoinName}", 5);
            }
        }
        
        private async UniTaskVoid HandleMoneyBagSpawned()
        {
            playerMenu.HandsMenu.SetMoneyBagActive(false);
            await economyModel.AddCoinAmount(-config.MoneyBagAmount, DisposeCancellationToken);
            notificationScreen.Show($"You spawned a bag of {config.MoneyBagAmount} {Constants.CoinName}!\n{economyModel.CoinAmount.Value} {Constants.CoinName} remaining", 5);
            playerMenu.HandsMenu.SetMoneyBagActive(true);
        }

        private void HandleMoneyBagGrabbed(SelectEnterEventArgs args)
        {
            if (TryGetMoneyBagActor(args.interactableObject, out var actor))
            {
                if (args.interactorObject.IsLeftHand())
                {
                    leftHandHoldingMoneyBag = true;
                }
                else if (args.interactorObject.IsRightHand())
                {
                    rightHandHoldingMoneyBag = true;
                }
            }

            TrySetMoneyBagActive();
        }

        private void HandleMoneyBagUnGrabbed(SelectExitEventArgs args)
        {
            if (TryGetMoneyBagActor(args.interactableObject, out var actor))
            {
                if (args.interactorObject.IsLeftHand())
                {
                    leftHandHoldingMoneyBag = false;
                }
                else if (args.interactorObject.IsRightHand())
                {
                    rightHandHoldingMoneyBag = false;
                }
            }

            TrySetMoneyBagActive();
        }

        private void TrySetMoneyBagActive()
        {
            playerMenu.MoneyBagCollectMenu.SetMoneyBagCollectionActive(IsHoldingMoneyBag);
        }

        private bool TryGetMoneyBagActor(IXRInteractable interactable, out MoneyBagActor actor)
        {
            return interactable.transform.TryGetComponent(out actor);
        }
    }
}