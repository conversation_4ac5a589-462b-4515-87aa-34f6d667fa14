using System;
using Cysharp.Threading.Tasks;
using Game.Models;
using Game.Views.Levels;
using Game.Views.UI.Screens.LevelSettings;
using Modules.Core;
using Modules.UI;
using VContainer;

namespace Game.Controllers.Levels
{
    public class LevelSettingsController : ControllerBase
    {
        private LevelModel levelModel;
        private LevelSettingsScreen levelSettingsScreen;
        private GameModel gameModel;

        [Inject]
        private void Construct(
            GameModel gameModel,
            LevelModel levelModel,
            IScreenManager screenManager)
        {
            this.gameModel = gameModel;
            this.levelModel = levelModel;

            levelSettingsScreen = screenManager.GetScreen<LevelSettingsScreen>();
            levelSettingsScreen.Hide();

            levelModel.OnLevelLoaded.Subscribe(HandleLevelLoaded).AddTo(DisposeCancellationToken);
        }

        private void HandleLevelLoaded(bool ok)
        {
            if (ok)
            {
                if (levelModel.Level.createdBy == gameModel.UserId)
                {
                    levelSettingsScreen.SetGameMode(levelModel.LevelConfig.GameMode);
                    levelSettingsScreen.SetPublishButtonText(levelModel.Level.isPublished);
                    levelSettingsScreen.SetDisableBuildingButtonText(levelModel.Level.buildingDisabled);
                    levelSettingsScreen.Show();
                }
                else
                {
                    levelSettingsScreen.Hide();
                }
            }
            else
            {
                levelSettingsScreen.Hide();
            }
        }
    }
}