using System;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Models;
using Game.Views.SmallShop;
using Modules.Core;
using VContainer;

namespace Game.Controllers.Shop
{
    public class SmallShopController : ControllerBase
    {
        private EconomyModel economyModel;
        private SmallShopManager smallShopManager;

        [Inject]
        private void Construct(GameModel gameModel, EconomyModel economyModel, SmallShopManager smallShopManager)
        {
            this.economyModel = economyModel;
            this.smallShopManager = smallShopManager;

            gameModel.IsInitialized.Where(ok => ok).Subscribe(_ => HandleInitialize()).AddTo(DisposeCancellationToken);
            economyModel.OnPurchaseCompleted.Subscribe(_ => HandlePurchaseCompeted()).AddTo(DisposeCancellationToken);
        }

        private void HandleInitialize()
        {
            smallShopManager.Initialize(economyModel.InventoryList);
        }

        private void HandlePurchaseCompeted()
        {
            smallShopManager.Refresh();
        }
    }
}