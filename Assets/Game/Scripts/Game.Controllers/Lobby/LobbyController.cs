using System;
using System.Reactive.Linq;
using Cysharp.Threading.Tasks;
using Game.Views.Levels;
using Game.Views.Lobby;
using Modules.Core;
using VContainer;

namespace Game.Controllers.Lobby
{
    public class LobbyController : ControllerBase
    {
        private LevelModel levelModel;
        private LobbySpaceManager lobbySpaceManager;

        [Inject]
        private void Construct(LevelModel levelModel, LobbySpaceManager lobbySpaceManager)
        {
            this.lobbySpaceManager = lobbySpaceManager;
            this.levelModel = levelModel;

            levelModel.OnLevelLoaded.Where(ok => ok).Subscribe(_ => HandleLevelLoaded()).AddTo(DisposeCancellationToken);
        }

        private void HandleLevelLoaded()
        {
            lobbySpaceManager.SetActiveLobby(levelModel.IsLobbyLevel);
        }
    }
}