using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core;
using Game.Models;
using Game.Views.DailyChallenges;
using Game.Views.UI.Screens.Challenges;
using Modules.Core;
using Modules.UI;
using VContainer;

namespace Game.Controllers.DailyChallenges
{
    public class DailyChallengesController : ControllerBase
    {
        private DailyChallengesModel dailyChallengesModel;
        private ChallengesScreen challengesScreen;
        private IAudioClient audioClient;
        private GameModel gameModel;
        private EconomyModel economyModel;

        private List<IDailyChallenge> DailyChallenges => dailyChallengesModel.DailyChallenges.Value;

        [Inject]
        private void Construct(
            IScreenManager screenManager,
            DailyChallengesModel dailyChallengesModel,
            GameModel gameModel,
            IAudioClient audioClient,
            EconomyModel economyModel)
        {
            this.economyModel = economyModel;
            this.gameModel = gameModel;
            this.dailyChallengesModel = dailyChallengesModel;
            this.audioClient = audioClient;

            challengesScreen = screenManager.GetScreen<ChallengesScreen>();
            gameModel.IsInitialized.Where(isLogged => isLogged).Subscribe(_ => UpdateDailyChallenges()).AddTo(DisposeCancellationToken);
            challengesScreen.OnClaimChallengeButtonClicked.Subscribe(i => ClaimDailyChallengeReward(i).Forget()).AddTo(DisposeCancellationToken);
        }

        private void UpdateDailyChallenges()
        {
            dailyChallengesModel.UpdateDailyChallenges();
            challengesScreen.SetChallengesScreenWidgetData(DailyChallenges.ConvertAll(challenge => new ChallengesScreenWidgetData(challenge)));
        }

        private async UniTaskVoid ClaimDailyChallengeReward(int index)
        {
            var result = await economyModel.ClaimDailyReward(DisposeCancellationToken);

            if (result.IsFail)
            {
                return;
            }

            gameModel.UpdateGiftClaimTime();
            DailyChallenges[index].IsClaimed = true;
            audioClient.Play(AudioKeys.ClaimChallenge);
            UpdateDailyChallenges();
        }
    }
}