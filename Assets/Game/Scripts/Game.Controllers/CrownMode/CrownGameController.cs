using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Fusion;
using Game.Core;
using Game.Core.Data;
using Game.Models;
using Game.Views.Consumeables;
using Game.Views.Interactables;
using Game.Views.Levels;
using Game.Views.Lobby;
using Game.Views.Players;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using VContainer;
using GameMode = Game.Core.Data.GameMode;

namespace Game.Controllers.CrownMode
{
    public class CrownGameController : ControllerBase
    {
        private const int Delay = 5;

        private LevelModel levelModel;
        private PlayersModel playersModel;
        private INetworkClient networkClient;
        private CrownModeModel crownModeModel;
        private LobbySpaceManager lobbySpaceManager;
        private InteractablesManager interactablesManager;
        private readonly GameTimer localTimer = new();

        private CancellationTokenSource masterClientCancellationTokenSource;
        private CancellationTokenSource runCancellationTokenSource;

        private IReadOnlyList<PlayerActor> Players => playersModel.Players;
        private bool IsCrownMode => levelModel.LevelConfig.GameMode == GameMode.Crown;

        [Inject]
        private void Construct(
            LevelModel levelModel,
            PlayersModel playersModel,
            INetworkClient networkClient,
            CrownModeModel crownModeModel,
            LobbySpaceManager lobbySpaceManager,
            InteractablesManager interactablesManager)
        {
            this.levelModel = levelModel;
            this.playersModel = playersModel;
            this.networkClient = networkClient;
            this.crownModeModel = crownModeModel;
            this.lobbySpaceManager = lobbySpaceManager;
            this.interactablesManager = interactablesManager;

            networkClient.IsConnected.Where(ok => ok).Subscribe(_ => HandleConnected()).AddTo(DisposeCancellationToken);
            playersModel.OnPlayerDestroyed.Subscribe(_ => HandlePlayerDestroyed()).AddTo(DisposeCancellationToken);
            networkClient.OnShutdown.Subscribe(_ => HandleShutdown()).AddTo(DisposeCancellationToken);
        }

        public override void Dispose()
        {
            base.Dispose();
            localTimer.Stop();
            runCancellationTokenSource.CancelAndDispose();
            masterClientCancellationTokenSource.CancelAndDispose();
        }

        private void HandleConnected()
        {
            if (!IsCrownMode)
            {
                return;
            }

            localTimer.Start(Delay);
            masterClientCancellationTokenSource.CancelAndDispose();
            masterClientCancellationTokenSource = new CancellationTokenSource();
            networkClient.IsMasterClient.Subscribe(TryRun).AddTo(masterClientCancellationTokenSource.Token);
        }

        private void HandlePlayerDestroyed()
        {
            if (!IsCrownMode || !networkClient.IsMasterClient.Value)
            {
                return;
            }

            CheckCrownPlayer();
        }

        private void HandleShutdown()
        {
            localTimer.Stop();
            runCancellationTokenSource.CancelAndDispose();
            masterClientCancellationTokenSource.CancelAndDispose();
        }

        private void TryRun(bool isMasterClient)
        {
            Stop();

            if (isMasterClient)
            {
                Run();
            }
        }

        private void Run()
        {
            runCancellationTokenSource = new CancellationTokenSource();
            crownModeModel.CrownPlayer.Skip(1).Subscribe(SetCrownPlayer).AddTo(runCancellationTokenSource.Token);
            crownModeModel.OnGameUpdated.Subscribe(_ => UpdateGame()).AddTo(runCancellationTokenSource.Token);

            CheckCrownPlayer();
        }

        private void Stop()
        {
            runCancellationTokenSource.CancelAndDispose();
        }

        private void UpdateGame()
        {
            if (!localTimer.IsExpired)
            {
                return;
            }

            if (crownModeModel.GameState.Value == GameState.Stopped)
            {
                if (!networkClient.HasNetworkActor<CrownActor>() && lobbySpaceManager.TryGetRandomItemSpawnPoint(out var point))
                {
                    interactablesManager.CreateActor(InventoryCodes.Crown, new Pose(point, Quaternion.identity));
                }

                crownModeModel.SearchCrown();
                localTimer.Start(Delay);
            }
            else if (crownModeModel.GameState.Value == GameState.SearchCrown)
            {
                if (crownModeModel.HasCrownPlayer)
                {
                    crownModeModel.Play();
                    localTimer.Start(Delay);
                }
            }
            else if (crownModeModel.GameState.Value == GameState.Playing)
            {
                if (crownModeModel.IsTimerExpired)
                {
                    crownModeModel.Pause();
                    crownModeModel.AwardCrownPlayer();
                    localTimer.Start(Delay);
                }
                else if (!crownModeModel.HasCrownPlayer)
                {
                    SetRandomCrownPlayer();
                    localTimer.Start(Delay);
                }
            }
            else if (crownModeModel.GameState.Value == GameState.Paused)
            {
                if (crownModeModel.IsTimerExpired)
                {
                    crownModeModel.Stop();
                    localTimer.Start(Delay);
                }
            }
        }

        private void CheckCrownPlayer()
        {
            if (crownModeModel.GameState.Value is not GameState.Playing || playersModel.TryGetPlayer(crownModeModel.CrownPlayer.Value, out _))
            {
                return;
            }

            SetRandomCrownPlayer();
        }

        private void SetCrownPlayer(PlayerRef playerRef)
        {
            crownModeModel.SetCrownPlayer(playerRef);
        }

        private void SetRandomCrownPlayer()
        {
            if (Players.Count == 0)
            {
                return;
            }

            var randomPlayer = Players.RandomItem();
            SetCrownPlayer(randomPlayer.StateAuthority);
        }
    }
}