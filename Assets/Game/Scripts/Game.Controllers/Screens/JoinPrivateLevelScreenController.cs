using System;
using Cysharp.Threading.Tasks;
using Game.Services;
using Game.Views.Levels;
using Game.Views.Lobby;
using Game.Views.Portals;
using Game.Views.UI.Screens.JoinPrivateLevel;
using Modules.Core;
using Modules.UI;
using VContainer;

namespace Game.Controllers.Screens
{
    public class JoinPrivateLevelScreenController : ControllerBase
    {
        private LevelModel levelModel;
        private LobbySpaceManager lobbySpaceManager;
        private JoinPrivateLevelScreen joinLevelScreen;

        private CustomPortalView CustomPortal => lobbySpaceManager.CustomPortal;

        [Inject]
        private void Construct(IScreenManager screenManager, LevelModel levelModel, LobbySpaceManager lobbySpaceManager)
        {
            this.levelModel = levelModel;
            this.lobbySpaceManager = lobbySpaceManager;

            joinLevelScreen = screenManager.GetScreen<JoinPrivateLevelScreen>();
            joinLevelScreen.OnJoinLevelClicked.Subscribe(data => HandleJoinLevel(data).Forget()).AddTo(DisposeCancellationToken);
        }

        private async UniTaskVoid HandleJoinLevel(JoinPrivateLevelArgs args)
        {
            var levelId = LevelDataParser.ValidateLevelId(args.name);

            if (levelId == levelModel.Level.id)
            {
                return;
            }

            var response = await levelModel.GetLevel(levelId, args.password, DisposeCancellationToken);
            if (response.IsFail)
            {
                joinLevelScreen.RenderInfo(response.HasInfo ? response.Info : "Something went wrong");
            }
            else
            {
                CustomPortal.Initialize(LevelDataParser.ToLevelMetaData(response.Data), args.password);
                joinLevelScreen.ResetScreen();
                joinLevelScreen.RenderInfo($"You have set the room: {args.name}\nPlease proceed through the portal.");
            }
        }
    }
}