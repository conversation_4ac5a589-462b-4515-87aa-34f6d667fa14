using System;
using Cysharp.Threading.Tasks;
using Game.Core.Data;
using Game.Models;
using Game.Services;
using Game.Views.Levels;
using Game.Views.Lobby;
using Game.Views.Portals;
using Game.Views.UI.Screens.CreateLevel;
using Modules.Core;
using Modules.UI;
using VContainer;

namespace Game.Controllers.Screens
{
    public class CreateLevelScreenController : ControllerBase
    {
        private GameModel gameModel;
        private LevelModel levelModel;
        private LevelSpaceManager levelSpaceManager;
        private LobbySpaceManager lobbySpaceManager;
        private CreateLevelScreen createLevelScreen;

        private CustomPortalView CustomPortal => lobbySpaceManager.CustomPortal;

        [Inject]
        private void Construct(IScreenManager screenManager, LevelModel levelModel, GameModel gameModel, LobbySpaceManager lobbySpaceManager)
        {
            this.gameModel = gameModel;
            this.levelModel = levelModel;
            this.lobbySpaceManager = lobbySpaceManager;
            createLevelScreen = screenManager.GetScreen<CreateLevelScreen>();

            createLevelScreen.OnCreateLevelClicked.Subscribe(args => HandleCreateLevel(args).Forget()).AddTo(DisposeCancellationToken);
        }

        private async UniTaskVoid HandleCreateLevel(CreateLevelArgs args)
        {
            var name = LevelDataParser.ValidateLevelName(args.name);
            var password = args.password;
            var worldTemplateId = args.worldTemplateId;
            var worldExtents = args.worldExtents;
            var createdByName = gameModel.UserName;
            var gameMode = args.gameMode;
            var response = await levelModel.CreateLevel(name, password, worldTemplateId, worldExtents, gameMode, createdByName, DisposeCancellationToken);

            if (response.IsFail)
            {
                var error = response.HasInfo ? response.Info : "Something went wrong";
                createLevelScreen.RenderInfo(error);
            }
            else
            {
                createLevelScreen.ResetScreen();
                createLevelScreen.RenderInfo($"You have created the room:\n{name}");
                CustomPortal.Initialize(LevelDataParser.ToLevelMetaData(response.Data));
            }
        }
    }
}