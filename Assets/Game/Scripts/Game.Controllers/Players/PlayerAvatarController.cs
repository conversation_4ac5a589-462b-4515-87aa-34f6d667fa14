using System;
using Cysharp.Threading.Tasks;
using Game.Core.Data;
using Game.Models;
using Game.Views.Avatars;
using Game.Views.Players;
using Modules.Core;
using VContainer;

namespace Game.Controllers.Players
{
    public class PlayerAvatarController : ControllerBase
    {
        private string currentHatCode;
        private string currentSuitCode;
        private string currentAvatarCode;
        private PlayersModel playersModel;
        private EconomyModel economyModel;
        private AvatarsConfig avatarsConfig;

        private PlayerActor LocalPlayer => playersModel.LocalPlayer.Value;

        [Inject]
        private void Construct(PlayersModel playersModel, EconomyModel economyModel, AvatarsConfig avatarsConfig)
        {
            this.economyModel = economyModel;
            this.avatarsConfig = avatarsConfig;
            this.playersModel = playersModel;

            economyModel.OnPurchaseCompleted.Subscribe(HandlePurchaseCompleted).AddTo(DisposeCancellationToken);
            playersModel.OnLocalPlayerBeforeCreated.Subscribe(HandleLocalPlayerBeforeSpawned).AddTo(DisposeCancellationToken);
            playersModel.OnLocalPlayerBeforeDestroyed.Subscribe(HandleLocalPlayerBeforeDestroyed).AddTo(DisposeCancellationToken);
        }

        private void HandleLocalPlayerBeforeSpawned(PlayerActor player)
        {
            player.SetAvatar(GetValidAvatarCode(playersModel.AvatarCode));
            player.SetSuit(GetValidSuitCode(playersModel.SuitCode));
            player.SetHat(GetValidHatCode(playersModel.HatCode));
        }

        private void HandleLocalPlayerBeforeDestroyed(PlayerActor player)
        {
            playersModel.AvatarCode = avatarsConfig.TryGetAvatarCode(LocalPlayer.AvatarId, out var avatarCode) ? avatarCode : null;
            playersModel.SuitCode = avatarsConfig.TryGetSuitCode(LocalPlayer.SuitId, out var suitCode) ? suitCode : null;
            playersModel.HatCode = avatarsConfig.TryGetHatCode(LocalPlayer.HatId, out var hatCode) ? hatCode : null;
        }

        private void HandlePurchaseCompleted(PurchaseResultData result)
        {
            if (result.rewardInventoryList == null || result.rewardInventoryList.Count == 0 || LocalPlayer == null)
            {
                return;
            }

            var hatEquipped = false;
            var suitEquipped = false;
            var avatarEquipped = false;

            result.rewardInventoryList.Sort((a, b) => b.Rarity.CompareTo(a.Rarity));
            foreach (var inventory in result.rewardInventoryList)
            {
                if (inventory.IsHat && !hatEquipped && avatarsConfig.HasHat(inventory.viewCode))
                {
                    LocalPlayer.SetHat(inventory.viewCode);
                    hatEquipped = true;
                }

                if (inventory.IsSuit && !suitEquipped && avatarsConfig.HasSuit(inventory.viewCode))
                {
                    LocalPlayer.SetSuit(inventory.viewCode);
                    suitEquipped = true;
                }

                if (inventory.IsAvatar && !avatarEquipped && avatarsConfig.HasAvatar(inventory.viewCode))
                {
                    LocalPlayer.SetAvatar(inventory.viewCode);
                    avatarEquipped = true;
                }
            }
        }

        private string GetValidAvatarCode(string code)
        {
            if (!avatarsConfig.TryGetAvatarId(code, out _) || !economyModel.InventoryList.FindAll(i => i.viewCode == code).Exists(i => i.IsOwned))
            {
                return avatarsConfig.DefaultAvatarCode;
            }

            return code;
        }

        private string GetValidSuitCode(string code)
        {
            if (!avatarsConfig.TryGetSuitId(code, out _) || !economyModel.InventoryList.FindAll(i => i.viewCode == code).Exists(i => i.IsOwned))
            {
                return null;
            }

            return code;
        }

        private string GetValidHatCode(string code)
        {
            if (!avatarsConfig.TryGetHatId(code, out _) || !economyModel.InventoryList.FindAll(i => i.viewCode == code).Exists(i => i.IsOwned))
            {
                return null;
            }

            return code;
        }
    }
}