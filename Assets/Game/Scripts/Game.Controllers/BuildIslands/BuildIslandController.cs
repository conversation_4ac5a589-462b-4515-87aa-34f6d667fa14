using System;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Fusion;
using Game.Views.BuildIslands;
using Game.Views.Levels;
using Game.Views.Players;
using Game.Views.UI.Screens.Notification;
using Modules.Core;
using Modules.Network;
using Modules.UI;
using VContainer;

namespace Game.Controllers.BuildIslands
{
    public class BuildIslandController : ControllerBase
    {
        private LevelModel levelModel;
        private PlayersModel playersModel;
        private NotificationScreen notificationScreen;
        private BuildIslandsManager buildIslandsManager;

        private PlayerActor LocalPlayer => playersModel.LocalPlayer.Value;

        [Inject]
        private void Construct(BuildIslandsManager buildIslandsManager, INetworkClient networkClient, PlayersModel playersModel, IScreenManager screenManager, LevelModel levelModel)
        {
            this.levelModel = levelModel;
            this.playersModel = playersModel;
            this.buildIslandsManager = buildIslandsManager;
            notificationScreen = screenManager.GetScreen<NotificationScreen>();

            networkClient.OnReadyMasterObjectSpawn.Subscribe(HandleReadyMasterObjectSpawn).AddTo(DisposeCancellationToken);
            networkClient.OnPlayerLeft.Subscribe(x => HandlePlayerLeft(x.runner, x.player)).AddTo(DisposeCancellationToken);
            networkClient.IsMasterClient.Where(ok => ok).Subscribe(_ => HandleMasterClient()).AddTo(DisposeCancellationToken);
            buildIslandsManager.OnClaimRequesting.Subscribe(HandleClaimRequesting).AddTo(DisposeCancellationToken);
        }

        private void HandleReadyMasterObjectSpawn(NetworkRunner runner)
        {
            if (!levelModel.IsLobbyLevel)
            {
                return;
            }

            buildIslandsManager.CreateAllBuildIslands();
        }

        private void HandleClaimRequesting(BuildIslandActor buildIsland)
        {
            if (!LocalPlayer)
            {
                return;
            }

            if (buildIslandsManager.HasClaimedBuildIsland(LocalPlayer.StateAuthority))
            {
                notificationScreen.Show("You have already claimed a island", 5);
            }
            else
            {
                buildIsland.Claim();
            }
        }

        private void HandlePlayerLeft(NetworkRunner runner, PlayerRef player)
        {
            if (!levelModel.IsLobbyLevel || !runner.IsSharedModeMasterClient)
            {
                return;
            }

            buildIslandsManager.CheckOwnersAndCobuilders();
        }

        private void HandleMasterClient()
        {
            if (!levelModel.IsLobbyLevel)
            {
                return;
            }

            buildIslandsManager.CheckOwnersAndCobuilders();
        }
    }
}