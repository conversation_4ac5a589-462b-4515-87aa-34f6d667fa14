using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Models;
using Game.Views.Levels;
using Game.Views.Players;
using Game.Views.Voxels;
using Game.Views.World;
using Game.Views.Zombies;
using MessagePipe;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using VContainer;
using Random = UnityEngine.Random;

namespace Game.Controllers.Zombies
{
    public class ZombieSpawnController : ControllerBase
    {
        private LevelModel levelModel;
        private VoxelSpaceManager voxelSpaceManager;
        private WorldBoundary worldBoundary;
        private ZombiesConfig zombiesConfig;
        private ZombiesManager zombiesManager;
        private INetworkClient networkClient;
        private TimeOfDayModel timeOfDayModel;
        private PlayersModel playersModel;

        private bool IsDay => timeOfDayModel.IsDay.Value;
        private bool UseZombie => levelModel.LevelConfig.UseZombies;
        private bool IsVoxelWorldLoaded => voxelSpaceManager.IsMapLoaded.Value;
        private bool IsMasterClient => networkClient.IsMasterClient.Value;

        [Inject]
        private void Construct(
            LevelModel levelModel,
            VoxelSpaceManager voxelSpaceManager,
            WorldBoundary worldBoundary,
            ZombiesConfig zombiesConfig,
            ZombiesManager zombiesManager,
            PlayersModel playersModel,
            TimeOfDayModel timeOfDayModel,
            INetworkClient networkClient,
            ISubscriber<ZombieKillArgs> zombieKillSubscriber)
        {
            this.levelModel = levelModel;
            this.voxelSpaceManager = voxelSpaceManager;
            this.zombiesConfig = zombiesConfig;
            this.networkClient = networkClient;
            this.playersModel = playersModel;
            this.timeOfDayModel = timeOfDayModel;
            this.zombiesManager = zombiesManager;
            this.worldBoundary = worldBoundary;

            playersModel.LocalPlayer.Subscribe(HandleLocalPlayer).AddTo(DisposeCancellationToken);
            playersModel.OnPlayerCreated.Subscribe(HandlePlayerCreated).AddTo(DisposeCancellationToken);
            zombieKillSubscriber.Subscribe(HandleKillZombie).AddTo(DisposeCancellationToken);
        }

        private void HandleLocalPlayer(PlayerActor localPlayer)
        {
            if (localPlayer == null)
            {
                return;
            }

            timeOfDayModel.IsDay.Where(isDay => !isDay).Subscribe(_ => HandleNight()).AddTo(localPlayer.destroyCancellationToken);
        }

        private void HandlePlayerCreated(PlayerActor player)
        {
            if (!UseZombie || IsDay || !IsMasterClient || !IsVoxelWorldLoaded)
            {
                return;
            }

            SpawnZombiesAroundPlayer(player, zombiesConfig.MaxSpawnZombieCountAroundPlayer);
        }

        private void HandleKillZombie(ZombieKillArgs args)
        {
            var delay = TimeSpan.FromSeconds(Random.Range(2f, 10f));
            var token0 = networkClient.DisconnectionCancellationToken;
            var token1 = DisposeCancellationToken;
            var linkedToken = CancellationTokenSource.CreateLinkedTokenSource(token0, token1).Token;

            UniTaskAsyncEnumerable.Timer(delay).Subscribe(_ =>
            {
                if (IsDay || !playersModel.TryGetPlayer(args.killer, out var player))
                {
                    return;
                }

                SpawnZombiesAroundPlayer(player, 1);
            }).AddTo(linkedToken);
        }

        private void HandleNight()
        {
            if (!UseZombie || !IsMasterClient)
            {
                return;
            }

            SpawnInitialZombies();
        }

        private void SpawnInitialZombies()
        {
            var counter = zombiesConfig.MaxSpawnZombieCountAroundPlayer;

            for (var i = 0; i < zombiesConfig.MaxZombieCount; i++)
            {
                if (counter <= 0 || zombiesManager.ZombieCount >= zombiesConfig.MaxZombieCount)
                {
                    return;
                }

                counter--;

                foreach (var player in playersModel.Players)
                {
                    SpawnZombiesAroundPlayer(player, 1);
                }
            }
        }

        private void SpawnZombiesAroundPlayer(PlayerActor player, int count)
        {
            var availableZombieCount = Mathf.Max(0, zombiesConfig.MaxZombieCount - zombiesManager.ZombieCount);
            var availableZombieCountAroundPlayer = Mathf.Max(0, zombiesConfig.MaxSpawnZombieCountAroundPlayer - zombiesManager.GetZombieCountInArea(player.transform.position));
            var resultCount = Mathf.Min(availableZombieCount, availableZombieCountAroundPlayer, count);
            var position = player.transform.position;

            for (var i = 0; i < resultCount; i++)
            {
                SpawnZombieAtRandomPoint(position);
            }
        }

        private void SpawnZombieAtRandomPoint(Vector3 origin)
        {
            if (!TryGetRandomPoint(origin, zombiesConfig.SpawnRange, out var point))
            {
                return;
            }

            zombiesManager.CreateRandomZombie(point, GetRandomRotation());
        }

        private bool TryGetRandomPoint(Vector3 origin, float radius, out Vector3 point)
        {
            point = origin + Random.insideUnitSphere.SetY(0) * radius;
            point.y = voxelSpaceManager.GetTerrainHeight(point);

            return point.y > 1 && !voxelSpaceManager.IsWaterVoxel(point) && worldBoundary.Contains(point);
        }

        private static Quaternion GetRandomRotation()
        {
            return Quaternion.AngleAxis(Random.Range(-180, 180), Vector3.up);
        }
    }
}