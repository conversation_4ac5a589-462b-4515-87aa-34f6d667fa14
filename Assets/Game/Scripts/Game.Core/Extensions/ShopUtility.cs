using Game.Core.Data;

namespace Game.Core.Extensions
{
    public static class ShopUtility
    {
        public static InventoryRarity GetRarity(int level)
        {
            if (level is >= 0 and <= 3)
            {
                return InventoryRarity.Common;
            }

            if (level is >= 4 and <= 6)
            {
                return InventoryRarity.Rare;
            }

            if (level is >= 7 and <= 9)
            {
                return InventoryRarity.Epic;
            }

            return InventoryRarity.Legendary;
        }
    }
}