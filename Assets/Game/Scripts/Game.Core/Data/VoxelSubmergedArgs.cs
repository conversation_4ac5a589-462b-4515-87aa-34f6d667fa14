namespace Game.Core.Data
{
    public class VoxelSubmergedArgs
    {
        public readonly int damage;
        public readonly float waterLevelWorldSpace;
        public readonly float waterLevelVoxel;
        public readonly bool isLava;

        /**
         * waterLevel is the level of the water in world space coordinates
         */
        public VoxelSubmergedArgs(int damage, float waterLevelWorldSpace, bool isLava, float waterLevelVoxel)
        {
            this.damage = damage;
            this.waterLevelWorldSpace = waterLevelWorldSpace;
            this.waterLevelVoxel = waterLevelVoxel;
            this.isLava = isLava;
        }
    }
}