Photon Voice 2 Change Log

v2.59 (May 13, 2025)
   PUN2: v2.49 (PUN2.49)
   RT/lib: v4.1.8.15
   PhotonVoiceApi: revision 7933
   Unity: 2019.4.39 or higher
   UPDATED: Photon3Unity3D.dll, Photon Realtime (LoadBalancing), Photon Chat to v4.1.8.15
Core:   
   ADDED: ByteStreamEncoder and ByteStreamDecoder for steaming byte buffers without processing
   ADDED: Custom1 - Custom9 items in Codec enum for user codec implementations
WebGL:
   BREAKING: panner node rolloff parameters removed from WebAudioAudioOut() constructor and set via new SetRefDistance() and SetMaxDistance() methods from Update() (similar to volume parameter)
   FIXED: TimeWorker crash during stop() call
PUN:
   MINOR: TransmitEnabled warning removed from PhotonVoiceView
Other:
   ADDED: DemoDataStream

v2.58 (March 03, 2025)
   PUN2: v2.49 (PUN2.49)
   RT/lib: v4.1.8.14
   PhotonVoiceApi: revision 7853
   Unity: 2019.4.39 or higher
   UPDATED: Photon3Unity3D.dll, Photon Realtime (LoadBalancing), Photon Chat to v4.1.8.14, PUN to v2.49
Core:
   ADDED: Codec enum extension methods IsAudio() and IsVideo() in a new Voice.Utility static class
   ADDED: AudioOutDelayControl.Delay property: a simplified delay API
   FIXED: noticeable audio playback start delay when resuming transmission after intentional pause (by sender or with Interest Groups): RemoteVoice considers an incoming frame as current rather than late if the gap with the previous frame is large and continues playback instead of waiting for a "valid" frame
   FIXED: possible noisy logging before the 1st packet arrives caused by useless ring buffer scan: decodeQueue() call moved after the synchronization wait call in decodeThread
   FIXED: AudioOutDelayControl.Service() clears the buffer correctly even if it's called rarely (once per sec. for WebGl)
   CHANGED: AudioOutDelayControl buffer size is at least 2 sec. (for WebGL)
   FIXED: AudioOutDelayControl buffer minimal size check used the wrong value and never worked
Unity:
   FIXED: VoiceComponentImpl.Level always returned 0
   FIXED: the scene marked as dirty after updating VoiceLogger.LogLevel from another inspector in Editor to not loose the change
   FIXED: UnityAudioOut zeroes the clip after creation because it may contain non-zero data (e.g. from previously created clip initialized with pcmreadercallback).
Android:
   CHANGED: audio capture session is initialized and destroyed asynchronously to prevent ui freezes
WebGL:
   FIXED: audio plays without interruption in background browser page due to Photon Realtime update and the following change
   CHANGED: VoiceConnection incoming packet dispatch call is triggered frequently enough by a worker, not in FixedUpdate() which is called only once per sec. in background browser page
iOS and Mac:
   CHANGED: AudioIn libraries set other apps ducking mode to minimum

v2.57 (December 13, 2024)
   PUN2: v2.45 (PUN2.45) 
   RT/lib: v4.1.8.8
   PhotonVoiceApi: revision 7794
   Unity: 2019.4.39 or higher
WebGL:
   ADDED: WebAudioAudioOut spatial ref and max distances control via AudioSource min and max distances
FMOD:
   FIXED: FMOD always used device 0 for recording
Fusion:
   FIXED: `LogLevel` related errors after importing Fusion 2.0.3

v2.56 (August 19, 2024)
   PUN2: v2.45 (PUN2.45) 
   RT/lib: v4.1.8.8
   PhotonVoiceApi: revision 7650
   Unity: 2019.4.39 or higher
   UPDATED: Photon3Unity3D.dll, Photon Realtime (LoadBalancing), Photon Chat to v4.1.8.8
Unity:
   FIXED: VoiceFollowClient leaves the room instead of disconnecting the server when follows the "not in room" leader's state: disconnect left the player in the room in an inactive state preventing subsequent connection with the same UserId
   CHNAGED: LoggerImpl cashes VoiceLogger.LogLevel in SetVoiceLogger() and Log() and uses the cashed value if Log() is called after the VoiceLogger instance is destroyed
   NOTE: this mitigates but not completely fixes the wrong logging level during scene unload if the global VoiceLogger is destroyed earlier than the logging component; use local VoiceLogger instances to avoid this
   CHANGED: do not create a voice settings file in the voice install sub-folder if voice is installed in the Packages folder (and then potentially not writable)
FMOD:
   ADDED: SpeakerFMOD event is always attached to the game object to support a spatializer in case it's attached to the event
WebGL:
   FIXED: WebAudioMicIn allocated a native buffer for captured audio 4 times larger than required
   FIXED: WebGL Speaker did not negate its position 'y' component

v2.55 (February 26, 2024)
   PUN2: v2.45 (PUN2.45) 
   RT/lib: v4.1.8.0
   PhotonVoiceApi: revision 7409
   Unity: 2019.4.39 or higher
   UPDATED: Photon3Unity3D.dll, Photon Realtime (LoadBalancing) to v4.1.8.0
   UPDATED: PUN to v2.45
   ADDED: visionOS support (Opus and WebRTC Audio libraries)
Core:
   BREAKING: ILogger.Log*() methods replaced with LogLevel enum and single Log() method accepting logging level
   BREAKING: added ILogger.Level property, that Voice code can check to avoid unnecessary Log() methods calls
   ADDED: LogLevel.Trace logging level
   BREAKING: LoadBalancingTransport is no longer ILogger, user code must provide Voice components with ILogger implementation (like Unity.Logger)
   ADDED: LoadBalancingTransport (and VoiceCLient created by the transport) uses private LBCLogger relying on LoadBalancingClient.DebugReturn() if not provided with an external ILogger
FMOD:
   FIXED: FMOD.AudioOutEvent playback (OutPos returns the position not wrapped by the event length)
PS4/PS5:
   CHANGED: Speaker.OutputPlugin property is exposed in Speaker's Editor inspector

v2.54 (January 08, 2024)
   PUN2: v2.43 (PUN2.43)
   RT/lib: v4.1.7.1
   PhotonVoiceApi: revision 7300
   Unity: 2019.4.39 or higher
Unity:
   FIXED: Speaker does not call audioOutput.Push() after stopping even if it receives a decoded packet
   FIXED: added a null-check in ConnectAndJoin.IsConnected that called sometimes before Start() where voiceConnection is initialized
   FIXED: removed SupportClass.StopAllBackgroundCalls() call from VoiceConnection.OnDestroy() (accidentally left after removal of AppQuit handling from VoiceConnection)
   REMOVED: LoadBalancingClient.ClientType update in VoiceConnection since we set it in the transport now
Fusion:
   ADDED: support for Fusion 2
   ADDED: additional security in case the Fusion's runner was not yet connecting during Fusionvoiceclient start
   FIXED: missing AutoConnectAndJoin checkbox in FusionVoiceClient inspector
   CHANGED: VoiceFollowClient.JoinVoiceRoom is virtual protected to allow room join customization
PUN:
   CHANGED: VoiceFollowClient.JoinVoiceRoom is virtual protected to allow room join customization
Core:
   FIXED: Set() was called sometimes on closed RemoteVoice.frameQueueReady AutoResetEvent
   FIXED: LoadBalancingTransport sets LoadBalancingClient.ClientType to Voice
   FIXED: VoiceClient.onVoiceRemove() referenced null in a log message if the voice was not found
   CHANGED: LoadBalancingTransport.LoadBalancingPeer.ChannelCount is set to at least 4 instead of length of Codec enum + 1 (the enum may be large while we normally do no need more than 4 channels)
iOS
   FIXED: AudioIn: routing audio out to earpiece (receiver) instead of loudspeaker since iOS 17 (and earlier versions for new iPad Pro) due to wrong initialization order (regression): session category is now set after AudioUnit creation
   FIXED: audio session 'Game' preset Mode changed to 'VoiceChat' from 'Default' that muted audio out sometimes
   CHANGED: Recorder default iOS Audio Session settings are 'Game' preset
   CHANGED: iOS Audio Session settings to 'Game' preset in all demos 
   NOTE: make sure that Recorder iOS Audio Session Preset is 'Game' in existing projects (it may be 'Custom' with wrong 'Default' mode)
   CHANGED: AudioIn: when stopping recording, session category is set to its previous values instead of Ambient
PS4/PS5:
   FIXED: the locking in PlayStationAudioOut.cs
   ADDED: AudioPluginPhotonVoice which enables support for routing audio output through the Unity Mixer before re-routing it directly to the Sony audioout APIs via a native mixer plugin on the PS4 and PS5
   ADDED: a new public 'OutputPlugin' field for PS4 and PS5 to Speaker.cs, which enables the user to select if he wants to use the classic PhotonVoiceAudioOutputPlugin (which does neither support nor require the use of a mixer) or the new AudioPluginPhotonVoice (which supports, but also requires the use of a mixer)
FMOD:
   FIXED: FMODHelpersMenu.cs was excluded from the project if FMOD was not enabled resulting in missing 'Photon Voice/Enable FMOD Integration' menu
   BREAKING: FMOD.AudioInReader no longer replaces -1 device id with 0 assuming that -1 is Voice API default mic: callers should always pass FMOD-specific device id
DemoVoiceUI:
   ADDED: FMOD microphone input support implemented via Recorder.InputFactory, added internal MicType and made other changes to integrate FMOD mic into the microphone list and differentiate it from AudioTone source, also factory based
   CHNAGED: RemoteSpeakerUI does not require Speaker (for easier manipulations in Editor)
   CHANGED: remote player UserId replaced with NickName + ActorNr in UI

v2.53 (September 06, 2023)
   PUN2: v2.43 (PUN2.43) 
   RT/lib: v4.1.7.1
   PhotonVoiceApi: revision 7141
   Unity: 2019.4.39 or higher
   UPDATED: Photon3Unity3D.dll, Photon Realtime (LoadBalancing) and Chat APIs to v4.1.7.1
   UPDATED: PUN to v2.43
PUN:
   CHANGED: 'following the Leader' logic extracted from PunVoiceClient into abstract VoiceFollowClient class
   ADDED: the voice client goes offline for errors in OpAuthenticate and OpJoinRoom until the next successfull Leader connection
   ADDED: ReconnectAndRejoin functionality: the voice room now has a PlayerTTL of 2 seconds; if the connection is lost, the client will attempt to get back into the room quickly
   CHANGED: if Leader isn't in an online room, the voice client will disconnect and stay offline
   BREAKING: there are other changes due to the rework that may affect the behavior
Fusion:
   BREAKING: FusionVoiceClient implemented via VoiceFollowClient, behavior is consistent with PUN now but can differ from the previous realization
   CHANGED: FusionVoiceClient: if networkRunner.GameMode is Single, Voice client does not connect
WebGL:
   FIXED: WebGL Audio AudioWorkletProcessor canceled processing if the first process() call buffer had no data that might happen during initialization, the remote voice did not play in this case
   ADDED: audio input device enumeration: WebAudioInEnumerator, returns the result asynchronously
   ADDED: audio input device selection: WebAudioMicIn() accepts 'deviceId' parameter
Core:
   CHANGED: VoiceClient.ThreadingEnabled is always false for UNITY_WEBGL, setter is ignored
   ADDED: IDeviceEnumerator.OnReady callback (asynchronous API, required by WebGL): both sync and async implementations call it when the device list is ready
   CHANGED: all DeviceEnumeratorBase inheritors call OnReady when the list is updated
DemoVoiceUI:
   CHANGED: audio input enumeration switched to asynchronous API to support WebGL
   CHANGED: ToneAudioReader is used for tone generator because ToneAudioPusher is not supported on WebGL

v2.52 (May 23, 2023)
   PUN2: v2.42 (PUN2.42) 
   RT/lib: 4.1.6.24
   PhotonVoiceApi: revision 6905
   Unity: 2019.4.39 or higher
   UPDATED: Photon3Unity3D.dll, Photon Realtime (LoadBalancing) and Chat APIs to v4.1.6.24
   UPDATED: PUN to v2.42
VoiceConnection:
   ADDED: 'C++ API Compatibility Mode' checkbox in VoiceConnection editor inspector
Recorder:
   ADDED: TargetPlayers property: only players specified in this property receive the voice info event and stream data, remote players also receive the voice remove event when removed from the list
   FIXED: SetAndroidNativeMicrophoneSettings() did not update anything
   CHANGED: Recorder uses Photon microphone type for WebGLPlayer even if Unity microphone type set
   CHANGED: Recorder uses AndroidAudioInParameters from Voice API directly instead of locally defined proxy struct NativeAndroidMicrophoneSettings
   BREAKING: Android microphone settings field name and type changed, all settings will be reset to true in Editor
   CHANGED: added public getters for Android microphone settings properties
Speaker:
   CHANGED: AudioOutDelayControl delay tolerance reduced (smaller drifts from the target delay)
   CHANGED: Speaker.Lag returns audioOutput.Lag or 0 if audioOutput is null (was -1 if not playing)
   FIXED: SpeakerFMOD editor inspector did not show SpeakerFMOD parameters: added SpeakerFMODEditor extending SpeakerEditor
iOS:
   FIXED: audio capture on iPhone 14: InputCallback is added and used for capture, RenderCallback still exists in the pipeline but does not do anything
Android:
   CHANGED: audio library: all permissions removed from the manifest, user must set permissions correctly in the application manifest
WebGL:
   CHANGED: Platform.CreateAudioInEnumerator() returns an instance of the new DeviceEnumeratorSingleDevice calss with the device named 'Default' instead of AudioInEnumeratorNotSupported for Unity WebGL platform
   CHANGED: Unity.AudioInEnumerator and UnityMicrophone rely on Unity Microphone API in Editor even if the platform is WebGL
Other:
   CHANGED: Unity methods are made protected virtual in UnityVoiceClient, FusionVoiceClient, PunVoiceClient and Speaker to allow component inheritance
   CHANGED: CustomEditor are marked as 'for child classes' in UnityVoiceClientEditor, FusionVoiceClientEditor, PunVoiceClientEditor to allow component inheritance
   BREAKING: VoiceConnection transport creation moved from constructor to Awake(): VoiceConnection.Client can be referenced by other components not earlier than in Start()
   CHANGED: code referencing VoiceConnection.Client moved from Awake() and OnEnabled() to Start() in PunVoiceClient, utility scripts and demos components
   CHANGED: in the above components, code form OnDisable() moved to OnDestroy()
   FIXED: OpusDecoder: IL2CPP-specific attribute is disabled for Unity prior to version 2021 in to avoid compilation error due to Unity bug (the affected method is experimental and unlikely to be used in older versions)
   REMOVED: UnityVoiceClientEditor photon components versions section
Demos:
   CHANGED: demos use Unity.AudioInEnumerator() as a recommended way to enumerate Unity microphones instead of UnityMicrophone.devices
   CHANGED: DemoVoicePun lists both Unity and Photon microphones in the device info
   CHANGED: DelayProcessor attenuates original signal to avoid overload
DemoVoiceUI:
   ADDED: mic intesity bar + color when transmitting
   ADDED: Android native microphone settings to demo ui

v2.51 (February 02, 2023)
   PUN2: v2.41 (PUN2.41) 
   RT/lib:4.1.6.18f2
   PhotonVoiceApi: revision 6697
   Unity: 2019.4.39 or higher
WebGL:
   ADDED: WebGL support via WebAudio API (Unity 2021.2 or higher required)
   NOTE: Safari is not supported
   NOTE: WebGL audio output implementation bypasses Unity audio system and uses WebAudio API context destination directly
   NOTE: Speaker applies AudioSource's volume and spatial blend, object position and AudioListener transform to WebGL audio output instance
   NOTE: WebGL audio output uses optimized audio graph if initialized with spatial blend = 1 or 0. To allow dynamic spatial blend, set AudioSource.spatialBlend to the value between 0 and 1 in Editor or call Speaker.RestartRecording() after changing it in runtime
   NOTE: AudioClipWrapper fails in WebGL after about 40 seconds with "memory access out of bounds" error in Unity AudioClip.GetData()
Recorder:
   FIXED: Recorder ignored voiceDetectionThreshold set in Editor
Android:
   CHANGED: native audio library: switching between audio devices improved, device selection order: bluetooth, usb, wired, speaker
PS4/PS5:
   FIXED: the "Mute Game Voice Chat" option in the PlayStation settings is now handled correctly

v2.50 (October 18, 2022)
   PUN2: v2.41 (PUN2.41) 
   RT/lib:4.1.6.18f2
   PhotonVoiceApi: revision 6358
   Unity: 2019.4.39 or higher (supporting Android x86_64 and UWP ARM64)
Migration Guide:
  - Backup
  - Remove Assets/Photon/PhotonVoice folder
  - Import Voice2 asset
  - Make sure that either a Speaker is attached to the prefab representing a voice (PhotonVoiceView or VoiceNetworkObject) or the Speaker prefab property is set in UnityVoiceClient, PunVoiceClient or FusionVoiceClient
  - Update Speaker Play Delay in Editor if you used non-default Playback Delay Settings
  - Set Recorder.RecordingEnabled to true if Recorder.AutoStart was true before the update
  Fusion:
  - Remove UnityVoiceClient (former VoiceConnection) and make sure that FusionVoiceClient component is attached (it should automatically replace no longer existing FusionVoiceBridge)
  FMOD:
  - Replace Speaker with SpeakerFMOD and remove missing script (no longer existing FMODSpeakerSetup)  
  Code:
  - Rename VoiceConnection -> UnityVoiceClient
  - Rename PhotonVoiceNetwork -> PunVoiceClient
  - Remove Logger.Is*Enabled checks
  - Replace Recorder.IsRecording with RecordingEnabled
  - Replace Recorder.MicrophonesEnumerator with an enumerator created with Platform.CreateAudioInEnumerator() in user code
  - Replace Recorder.UnityMicrophoneDevice and Recorder.PhotonMicrophoneDeviceId with Recorder.MicrophoneDevice
  - Replace Recorder.PlaybackDelayMinSoft with Recorder.PlayDelay and remove occurrences of PlaybackDelayMaxSoft and PlaybackDelayMaxHard
  - Remove all occurrences of GlobalPlaybackDelayMinSoft, GlobalPlaybackDelayMaxSoft, GlobalPlaybackDelayMaxHard, PlaybackDelayMinSoft, PlaybackDelayMaxSoft, PlaybackDelayMaxHard
  - Update other API calls according to the changes list below
  - If the code taken from a Voice2 demo does not compile, see how this demo has changed in the package
PhotonVoiceAPI:
   BREAKING: AudioOutDelayControl<T>.OutPos and FMOD.AudioInEnumerator() signatures changed
   FIXED: the output buffer cleanup procedure called OutWrite(zeroFrame) at least once in each AudioOutDelayControl.Service() call even if OutPos did not change (could be hundreds of times per sec. in Unity because Service() is called in Update())
   FIXED: Unity MicWrapperPusher (using OnAudioFilterRead)
   FIXED: PS4/PS5: Unity MicWrapper/MicWrapperPusher sampling rate selection from the fixed set supported by the platform
   FIXED: OSX native audio input: AudioComponentInstanceDispose is called on audio unit when destroying component: other sounds ducked on capture initialization restore their normal level
   CHANGED: LocalVoiceFramed<T>.PushData() does not skip audio processing and encoding if voiceClient is not connected (set TransmitEnabled to false to skip processing)
   ADDED: LocalVoiceFramedBase.RemoveProcessor()
VoiceConnection:
   CHANGED: VoiceConnection is not instantiated directly, but is a base class for Unity, PUN and Fusion integration implementations: extended class needs to define how to use PrimaryRecorder and SpeakerPrefab
   CHANGED: initialization: a component that owns or creates the Recorder (PUN.PhotonVoiceView or Fusion.VoiceNetworkObject or VoiceConnection instance itself owning PrimaryRecorder) calls VoiceConnection.AddRecorder() to register and initialize the Recorder
   BREAKING: removed VoiceConnection.AutoCreateSpeakerIfNotFound, VoiceConnection does not create Speaker if it's not specified: SpeakerPrefab must be set for Speaker creation
   BREAKING: removed SpeakerFactory, use SpeakerPrefab with proper Speaker implementation
   ADDED: VoiceConnectionClient.UsePrimaryRecorder property (PUN and Fusion integrations start PrimaryRecorder directly if true)
UnityVoiceClient:
   CHANGED: UnityVoiceClient is a Unity integration extending VoiceConnection, it uses PrimaryRecorder for outgoing stream and instantiates Speakers from SpeakerPrefab
   CHANGED: ignores UsePrimaryRecorder and always starts PrimaryRecorder as before
   CHANGED: UnityVoiceClientEditor does not hide app settings when client is connected
   ADDED: application settings can be optionally read from VoiceAppSettings scriptable object available in Editor
PUN:
   BREAKING: renamed PhotonVoiceNetwork -> PunVoiceClient
   CHANGED: PunVoiceClient creates Recorder only if photonView.IsMine and always creates Speaker
   ADDED: PrimaryRecorder started directly if VoiceConnection.UsePrimaryRecorder is set (PhotonVoiceView prefab is not required)
   ADDED: SpeakerPrefab instantiated directly on PunVoiceClient object if userData is null (remote voice of PrimaryRecorder started directly)
   REMOVED: PunVoiceClient.WorkInOfflineMode: Voice does not support offline mode   
   REMOVED: PhotonVoiceView.UsePrimaryRecorder: PrimaryRecorder is used automatically if the Recorder is not found in PhotonVoiceView object hierarchy
   CHANGED: PhotonVoiceView Speaker initialization: either the Speaker found in the object hierarchy or PunVoiceClient.SpeakerPrefab is used
   ADDED: PunVoiceClient.UsePunAppSettings public property (this setting was only available in editor)
   BREAKING: removed PhotonVoiceView serialized 'speakerInUse' and 'recorderInUse': it's no longer possible to specify Recorder and Speaker via direct reference in Editor
   BREAKING: removed PhotonVoiceView.RecorderInUse and SpeakerInUse public setters: it's not longer possible to set Recorder and Speaker in runtime
   BREAKING: removed PhotonVoiceView.AutoCreateRecorderIfNotFound (Recorders never created automatically)
   BREAKING: removed PhotonVoiceView.SetupDebugSpeaker: Speaker is always created if specified
   BREAKING: removed PhotonVoiceView.IsSetup
   BREAKING: removed PhotonVoiceView.IsSpeaker, use this.SpeakerInUse != null
   BREAKING: removed PhotonVoiceView.IsRecorder, use this.RecorderInUse != null
   BREAKING: removed PhotonVoiceView.IsSpeakerLinked, use this.SpeakerInUse.IsLinked
   BREAKING: removed PhotonVoiceView.IsPhotonViewReady, assuming always ready
Fusion:
   REMOVED: FusionVoiceBridge referenced VoiceConnection
   ADDED: FusionVoiceClient extending VoiceConnection
   NOTE: remove existing UnityVoiceClient (former VoiceConnection) and make sure that FusionVoiceClient component is attached (it should automatically replace no longer existing FusionVoiceBridge)
   ADDED: PrimaryRecorder started directly if VoiceConnection.UsePrimaryRecorder is set (VoiceNetworkObject prefab is not required)
   ADDED: SpeakerPrefab instantiated directly on FusionVoiceClient object if userData is null (remote voice of PrimaryRecorder started directly)
   CHANGED: VoiceNetworkObject Speaker initialization: either the Speaker found in the object hierarchy or specified FusionVoiceClient.SpeakerPrefab is used
   BREAKING: removed VoiceNetworkObject serialized 'speakerInUse' and 'recorderInUse': it's no longer possible to specify Recorder and Speaker via direct reference in Editor
   BREAKING: removed VoiceNetworkObject.RecorderInUse and SpeakerInUse public setters: it's not longer possible to set Recorder and Speaker in runtime
   BREAKING: removed VoiceNetworkObject.AutoCreateRecorderIfNotFound (Recorders never created automatically)
   BREAKING: removed VoiceNetworkObject.SetupDebugSpeaker: Speaker is always created if specified
   BREAKING: removed VoiceNetworkObject.IsSetup
   BREAKING: removed VoiceNetworkObject.IsSpeaker, use this.SpeakerInUse != null
   BREAKING: removed VoiceNetworkObject.IsRecorder, use this.RecorderInUse != null
   BREAKING: removed VoiceNetworkObject.IsSpeakerLinked, use this.SpeakerInUse.IsLinked
   BREAKING: VoiceNetworkObject Recorder initialization: either PrimaryRecorder or attached Recorder is used without falling back to the latter if UsePrimaryRecorder is true but PrimaryRecorder is not set
   REMOVED: Fusion 0-Minimal demo, it's almost the same as 1-SpeakerPrefab demo but relies on automatic Speaker creation which is no longer supported
Recorder:
   ADDED: Recorder.RecordingEnabled, controls recording state on start and while Recorder is running, exposed in Editor, default is true
   BREAKING: removed Recorder.IsRecording, use RecordingEnabled instead
   BREAKING: removed Recorder.AutoStart, use RecordingEnabled instead
   BREAKING: removed Recorder.StartRecording/StopRecording, use RecordingEnabled instead
   BREAKING: removed Recorder.RequiresRestart, obsolete RequiresInit obsolete ReInit()
   BREAKING: removed 'force' parameter of Recorder.RestartRecording()
   CHANGED: Recorder.RestartRecording() restarts only if RecordingEnabled is true
   CHANGED: Recorder.RestartRecording() returns RecordingEnabled value for convenience
   CHANGED: Recorder.TransmitEnabled is true by default
   BREAKING: renamed Recorder.RecordOnlyWhenJoined to RecordWhenJoined, it updates RecordingEnabled property only when joining or leaving the room, it's still possible to record outside the room, the setter does not tries to start or stop the Recorder
   BREAKING: removed Recorder.RecordOnlyWhenEnabled, 'enable' state is not processed (OnEnable/OnDisable removed): since Update() restarts the Recorder, restart won't work while disabled
   BREAKING: MicrophonePermission helper starts associated Recorder instead of manipulating with its AutoStart property which is no longer available
   REMOVED: RestartRecording button in RecorderEditor.OnInspectorGUI
   BREAKING: removed Recorder.IsInitialized
   BREAKING: removed Recorder.Init (made private)
   BREAKING: removed Recorder.MicrophonesEnumerator, it does not affect Recorder behavior (except checks removed below): create device enumeration instances in user code if necessary
   BREAKING: removed Recorder.UnityMicrophoneDevice
   BREAKING: removed Recorder.PhotonMicrophoneDeviceId
   BREAKING: removed (WSA) Recorder.PhotonMicrophoneDeviceIdString
   BREAKING: removed obsolete Recorder.PhotonMicrophoneEnumerator
   BREAKING: removed deprecated Recorder.ReactOnSystemChanges (use AudioChangesHandler instead)
   BREAKING: removed Recorder.IsDefaultUnityMic()
   BREAKING: removed Recorder.TrySamplingRateMatch and sampling rate matching logic
   CHANGED: 3 platform-specific device ids properties and manipulations over them are replaced with uniform single DeviceInfo exposed in existing MicrophoneDevice property
   REMOVED: IsValidUnityMic() and CheckIfThereIsAtLeastOneMic() checks based on microphone list which is no longer available in Recorder, local voice creation should fail gracefully w/o these checks
   CHANGED: the same IDeviceEnumerator instance is used for Photon and Unity microphones types, GetMicrophonesEnumerator() code moved to MicrophonesEnumerator getter and refactored
   CHANGED: if Recorder.SourceType is Factory and InputFactory is not set, it's assigned with ToneAudioReader() factory automatically
   REMOVED: obsolete SampleTypeConv enum and TypeConvert property
   REMOVED: obsolete Recorder.AudioGroup
   FIXED: updating voiceDetectionDelayMs and voiceDetection with values from VoiceDetector overrode deserialized values (GetActivityDelayFromDetector and GetStatusFromDetector removed)
Recorder Editor:
   REMOVED: microphones list (with enumerators and AudioSettings.OnAudioConfigurationChanged handling)
   REMOVED: 'Enabled WebRtcAudioDsp component' button
   ADDED: 'Remove WebRtcAudioDsp component' button available if the component is present
   ADDED: 'Remove AudioChangesHandler component' button available if the component is present
   CHANGED: iOS and Android specific session parameters and APIs are present on all platforms
   CHANGED: iOS and Android settings are visible regardless of Recorder MicrophoneType
   CHANGED: iOS audio session presets and custom settings handlinging Editor (Recorder stores the preset for use in Editor only; in runtime, only iosAudioSessionParameters are used)
   CHANGED: RecorderEditor no longer hides fields 'non-relevant' to the currently selected SourceType
   CHANGED: Recorder.DebugEcho is always visible in Editor regardless of InterestGroup
   CHANGED: WebRtcAudioDsp control is available on all platforms even if the component is not supported
AudioChangesHandler:
   REMOVED: HandleConfigChange and AudioSettings.GetConfiguration() change detection: it does not make sense because this configuration is for output and its change can't be triggered by microphone configuration change
   NOTE: AudioSettings.OnAudioConfigurationChanged handling is preserved even though it's not triggered by microphone configuration change according to the documentation
   REMOVED: StartWhenDeviceChange, set Recorder.RecordingEnabled to true instead
   REMOVED: UseNativePluginChangeNotifier and UseOnAudioConfigurationChanged: Photon AudioInChangeNotifier used if supported by the platform, otherwise Unity's AudioSettings.OnAudioConfigurationChanged handled
   CHANGED: removed iOS_AlwaysHandleDeviceChange and Android_AlwaysHandleDeviceChange with confusing semantics and replaced with HandleDeviceChangeIOS and HandleDeviceChangeAndroid overriding HandleDeviceChange for correspondent platforms
Speaker:
   BREAKING: removed VoiceConnection.globalPlaybackDelaySettings
   BREAKING: removed PlaybackOnlyWhenEnabled
   BREAKING: removed PlaybackDelayMinSoft, PlaybackDelayMaxSoft, PlaybackDelayMaxHard
   BREAKING: removed Speaker.SetPlaybackDelaySettings() methods
   BREAKING: removed obsolete PlayDelayMs
   BREAKING: removed PlaybackDelaySettings wrapper, Speaker uses AudioOutDelayControl.PlayDelayConfig directly   
   ADDED: PlayDelayConfig getter and setter
   ADDED: PlayDelay getter and setter: updates PlayDelayConfig based on the single given value (simplified API)
   CHANGED: Editor exposes single Speaker.PlayDelay value instead of 3 values of PlayDelayConfig
   CHANGED: play delay settings are never checked but passed as is to AudioOut which corrects them if necessary
   BREAKING: removed Actor property, use RemoteVoice.PlayerId instead
   BREAKING: removed PlaybackStarted
   BREAKING: removed StartPlayback() and StopPlayback() (hopefully used only internally)
   BREAKING: RestartPlayback() does not accept 'reinit' parameter, it always stops and starts audioOutput
   BREAKING: USE_ONAUDIOFILTERREAD conditional compilation replaced with SpeakerAudioFilterRead extending Speaker
   BREAKING: removed AudioOutFactory and GlobalAudioOutFactory
   ADDED: RestartOnDeviceChange property: if true (default), the Speaker is restarted in AudioSettings.OnAudioConfigurationChanged handler (FIXED: 'AudioClip.SetData failed; AudioClip UnityAudioOut contains no data' errors after output device change like when switching to bluetooth on Android)
WebRTCAudioDsp:
   ADDED: missing AgcTargetLevel property
   ADDED: IsSupported property
   BREAKING: removed ForceNormalAecInMobile, proc.AECMobile set if Application.isMobilePlatform is true
   BREAKING: removed AutoRestartOnAudioChannelsMismatch, never restarts on channel count mismatch in OnAudioOutFrameFloat, OnAudioConfigurationChanged should handle this change
   REMOVED: obsolete and not used AECMobile
   REMOVED: obsolete and not used AECMobileComfortNoise
   CHANGED: NoiseSuppression is true by default
   CHANGED: all controls are always visible in WebRTCAudioDspEditor
MicAmplifier:
   FIXED: AmplificationFactor was cast to short for MicAmplifierShort, allowing only integer factors
   REMOVED: BoostValue which only added harmful constant offset to the audio signal
   REMOVED: unused MaxBefore and MaxAfter
FMOD:
   BREAKING: Speaker's companion FMODSpeakerSetup replaced with SpeakerFMOD extending Speaker
   BREAKING: removed FMODRecorderSetup.MicrophoneDevice, FMODMicrophoneDeviceId: Recorder MicrophoneDevice is used instead
   BREAKING: FMODRecorderSetup.autoRestartOnChange serialized field, always restarting if FMODMicrophoneDeviceId property has changed
   REMOVED: microphones list from FMODRecorderSetupEditor
DemoVoiceUI: 
   CHANGED: Unity type microphones are processed consistently with Photon type, via enumerator
   CHANGED: no more special processing for WSA platform microphones
   CHANGED: the demo relies on its own IDeviceEnumerator instances
   CHANGED: menu items hold only Recorder.MicType and DeviceInfo that significantly simplified exchange between menu and Recorder MicrophoneType/MicrophoneDevice
   CHANGED: Unity and Photon default microphones added to the dropdown
   ADDED: AGC Target level slider
Logging:
   CHANGED: single global logging level applies to all voice components, it's exposed by VoiceConnection instance (UnityVoiceClient, PunVoiceClient, FusionVoiceClient) in Editor
   NOTE: global logging level is stored in VoiceLogger object at the root of the scene, different levels can still be applied: a Voicelogger component added to the object sets logging level for voice components attached to this object and all its descendants
   BREAKING: removed ILoggable (LogLevel, Logger), ILoggableDependent (IgnoreGlobalLogLevel)
   BREAKING: removed VoiceComponent.Logger, LogLevel, IgnoreGlobalLogLevel
Other:
   BREAKING: removed VoiceLogger.IsErrorEnabled, IsWarningEnabled, IsInfoEnabled and IsDebugEnabled used mostly for redundant logging level checks, such checks pollute sources and usually don't improve the performance, use VoiceLogger.LogLevel if the check is certainly necessary
   BREAKING: removed VoiceComponent.CurrentPlatform, use Application.platform instead
   REMOVED: PUN, PhotonRealtime and PhotonChat demos
   CHANGED: simple WaveWriter (used in SaveOutgoingStreamToFile and SaveIncomingStreamToFile) extracted from CSCore library and placed in Code/UtilityScripts
   REMOVED: CSCore library
   FIXED: SaveOutgoingStreamToFile uses AddPostProcessor() instead of AddPreProcessor(), otherwise the stream in Process() mismatches VoiceInfo in PhotonVoiceCreated
   BREAKING: removed placeholders for deleted files
   ADDED: RecorderPreset component overriding Recorder (Microphone Type, DSP Enabled) and WebRtcAudioDsp (AEC, VAD) settings per platform

v2.32-v2.49
   Skipped due to significant changes

v2.31 (March 22nd, 2022)
   PUN2: v2.40 (14f52ea) [RT/lib:********]
   PhotonVoiceApi: revision 6358
   ADDED: Photon Fusion integration scripts (the old GitHub repo for the integration will be archived).
   FIXED: CheckIfThereIsAtLeastOneMic should be used only if Recorder.SourceType is Microphone.
   CHANGED: if SpeakerPrefab fails, try AutoCreateSpeakerIfNotFound if enabled.
   CHANGED: VoiceConnection.SpeakerPrefab will be accepted if Speaker component is disabled.
   CHANGED: Recorder.CheckIfMicrophoneIdIsValid is now public static.
   CHANGED: AudioSource is no longer a required component for the Speaker in PHOTON_VOICE_FMOD_ENABLE context.
   CHANGED: UnityEngine.Object null ref. & destroy checks.
   CHANGED: Recorder will always set PhotonMicrophoneDeviceId to -1 if default MicrophoneDevice is set and Photon microphone type is used.
   CHANGED: Recorder will always override requested sampling rate w/ effective one (if changed).
   CHANGED: [WebRtcAudioDsp] effective Recorder.SamplingRate choice when requested value is incompatible w/ WebRtcAudioDsp (now 12kHz -> 16kHz, before it was 12kHz -> 48kHz).
   ADDED: [Editor/WebRtcAudioDsp] in edit mode, warning dialog box shown when requested SamplingRate is incompatible w/ WebRtcAudioDsp.
   CHANGED: [Editor/WebRtcAudioDsp] handling WebRtcAudioDsp disabled vs. not attached in Recorder custom Editor.
   CHANGED: [Editor] microphone device selection info dialog box always displayed when dropdown is available.
   CHANGED: [Editor] minor, AudioChangesHandler script component icon set to Photon Voice icon.
   CHANGED: minor, AudioChangesHandler open brackets code format to be consistent w/ overall code style guidelines.

v2.30 (March 16th, 2022)
   PUN2: v2.40 (14f52ea) [RT/lib:********]
   PhotonVoiceApi: revision 6358
   BREAKING: [Nintendo Switch] in order for microphone input to work properly add AudioChangesHandler to the same GameObject as the Recorder.
   BREAKING: [Nintendo Switch] update to Photon-NintendoSwitch-Voice-Plugin-Sdk_v4-0-0-2 or higher for proper microphone input support.
   ADDED: [macOS] Apple Silicon M1 support.
   ADDED: AudioChangesHandler which is now the preferred way to handle device and config changes on all platforms (Recorder.ReactOnSystemChanges will be deprecated).
   CHANGED: Recorder can now auto start when mic might be available (using AudioChangesHandler).
   FIXED: Recorder will check if mic is available before starting if mic type is Photon.
   ADDED: Recorder.CheckIfThereIsAtLeastOneMic: this is now a necessary condition to (auto) start recording.
   ADDED: Speaker.CustomAudioOutFactory (e.g. will be used for upcoming refactored FMOD integration).
   CHANGED: Recorder.CheckAndAutoStart refactored to debug log exact check failure(s).
   CHANGED: [Logging] minor; default voice component log level increased to WARN.
   CHANGED: [PUN integration] when PhotonVoiceNetwork.PrimaryRecorder is not set, try to find Recorder on PhotonVoiceView's prefab first.
   ADDED: [PUN integration] warning log when no Recorder is found from PhotonVoiceView.

v2.29 (December 24th, 2021)
   PUN2: v2.40 (14f52ea) [RT/lib:********]
   PhotonVoiceApi: revision 6315
   ADDED: [Nintendo Switch] audio input (Photon microphone type) and audio devices change detection via native plugin (requires separate add-on).
   FIXED: Recorder now sends PhotonVoiceRemoved message inside OnDestroy as component may be destoryed (processors need to disposed of) while GameObject remains.
   FIXED: [WebRtcAudioDsp.AEC] issues due to thread safety (Unity main thread vs. OnAudioFilterRead thread).
   ADDED: [WebRtcAudioDsp.AEC] AutoRestartOnAudioChannelsMismatch to toggle behaviour (default is true).
   ADDED: [WebRtcAudioDsp.AEC] more logging to help debugging in case of issues.
   CHANGED: [WebRtcAudioDsp.AEC] more refactoring to help handle edge cases better/clearer.
   CHANGED: [WebRtcAudioDsp] auto restart when needed due to changes detected using OnAudioConfigurationChanged.
   CHANGED: [WebRtcAudioDsp] minor, ForceNormalAecInMobile is made field instead of property.
   CHANGED: disposing of Recorder.inputSource and setting it to null early in all case of recording start failures.
   FIXED: [Editor Only, WebGL] minor, error due to empty Microphone.devices.

v2.28.2 (December 3rd, 2021)
   PUN2: v2.40 (14f52ea) [RT/lib:********]
   PhotonVoiceApi: revision 6306
   FIXED: NullReferenceException when Recorder.StopRecordingWhenPaused is enabled as Recorder.HandleApplicationPause was trying to start recording without checking if it should.
   FIXED: [WebRtcAudioDsp.AEC] NullReferenceException when WebRtcAudioDsp.AEC is enabled (introduced in previous version where we subscribe to AudioOutCapture event before we initialize AEC in WebRTCAudioProcessor).
   FIXED: [iOS] resets audio session category to defaults values (CategoryAmbient, ModeDefault, CategoryOptionMixWithOthers) on cleanup (so you can set volume to zero for instance when app is closed).
   FIXED: VoiceConnection.OnRemoteVoiceInfo() was setting the output handler for the remote stream even if the codec was not Opus, this prevented video component sharing this connection from handling the stream.
   ADDED: [Logging] voice networking client's operation response errors (e.g. when join voice room fails).
   ADDED: [WebRtcAudioDsp.AEC] WebRtcAudioDsp.AecOnlyWhenEnabled: as the name implies AEC will be used only when component isActiveAndEnabled (default true).
   CHANGED: [WebRtcAudioDsp.AEC] refactoring started in previous version should be done.
   ADDED: minor, WebRtcAudioDsp.IsInitialized (simple null check for internal processor class).
   FIXED: minor, WebRtcAudioDsp's supported platforms custom define condition.
   FIXED: [Editor only] minor, proper warn message text in custom WebRtcAudioDspEditor (only AEC does not work when component is not enabled).
   FIXED: minor XML tag missing in PhotonVoiceEditorUtils.

v2.28.1 (November 30th, 2021)
   PUN2: v2.40 (14f52ea) [RT/lib:********]
   PhotonVoiceApi: revision 6296
   FIXED: Speaker.RestartPlayback was calling StartPlayback instead of StopPlayback first.
   FIXED: [Editor only] minor, custom inspector error when BestRegionSummaryInPreferences format is broken.
   FIXED: [Editor only] minor, custom inspector warning shown in wrong conditions (WebRtcAudioDsp.AEC enabled and Photon microphone type is not used).
   FIXED: AudioOutCapture added only when WebRtcAudioDsp.AEC is enabled. 
   CHANGED: [WebRtcAudioDsp.AEC] the getter now returns actual state of AEC when the WebRtcAudioDsp is initialized. 
   CHANGED: [WebRtcAudioDsp.AEC] when disabled, AudioOutCapture is auto destroyed if it was created by WebRtcAudioDsp.

v2.28 (November 10th, 2021)
   PUN2: v2.39 (3d2cb9e) [RT/lib:4.1.6.10]
   PhotonVoiceApi: revision 6268
   FIXED: passing correct default Photon microphone ID (-1 instead of -128).
   FIXED: compilation in 2021.2+ due to namespace change.
   FIXED: crash in macOS when device changes due to Unity API call from non main thread by adding internal Recorder.MicrophoneDeviceChangeDetected flag & restarting recording from VoiceConnection.
   CHANGED: [BREAKING] VoiceConnection.SimpleSpeakerFactory is now a fallback factory in all cases where VoiceConnection.SpeakerFactory does not return Speaker.
   ADDED: VoiceConnection.TryLateLinkingUsingUserData (to be used w/ Photon Fusion & any other product integration).
   CHANGED: VoiceConnection.GlobalRecordersLogLevel & VoiceConnection.GlobalSpeakersLogLevel refactored: we now properly check IgnoreGlobalLogLevel per Speaker, update initliazedRecorders only in Editor (no more FindObjectOfType) and build.
   CHANGED: RemoteVoiceLink refactoring: for better logging & easier / simpler remote voice streams cache handling.

v2.27.1 (October 26th, 2021)
   PUN2: v2.39 (3d2cb9e) [RT/lib:4.1.6.10]
   PhotonVoiceApi: revision 6245
   FIXED: [Android] removed Camera permission added in previous version.
   FIXED: background thread name length due to some constraints in some platform.
   FIXED: [logging] minor, log level used for PhotonVoiceApi's AudioSyncBuffer classes: debug instead of info. (this will avoid 'underrun' / 'overrun' Speaker log noise/spam)

v2.27 (October 06th, 2021)
   PUN2: v2.37 (a9f7ce2) [RT/lib:4.1.6.7]
   PhotonVoiceApi: revision 6226
   CHANGED: [BREAKING macOS] native libs 'bundles' are no longer folders but files instead, you may want to remove those folders (AudioIn.bundle, opus_egpv.bundle, webrtc-audio.bundle) before updating or do a clean install of this version.
   CHANGED: [BREAKING] by default the new NON-ALLOC version of LoadBalancingTransport is used in VoiceConnection. You can disable by commenting out "#define USE_NEW_TRANSPORT".
   FIXED: Voice logging (VoiceConnection LogLevel, PhotonVoiceAPI) decoupled from Photon Realtime logging (LoadBalancingClient, NetworkLogging, LoadBalancingPeer.DebugOut).
   FIXED: [PhotonVoiceApi, native lib, macOS] app freeze due to audio device change by replacing Unity's OnAudioConfigurationChanged usage with custom native plugin.
   FIXED: [PhotonVoiceApi, native lib, Android] attempts were made to make audio bluetooth devices switch work smoothly when using Photon microphone type.
   ADDED: [Experimental macOS] M1 support.
   ADDED: [UtilityScript] MicrophonePermission for iOS and Android used in DemoVoiceUI.
   ADDED: a way to force reinitialize AudioOut in Speaker.RestartPlayback via method param.
   ADDED: VoiceConnection.MaxDatagrams & VoiceConnection.SendAsap as used by PUN in PhotonHandler.
   ADDED: VOICE_DISPATCH_IN_FIXEDUPDATE & VOICE_DISPATCH_IN_LATEUPDATE options (custom defines) in VoiceConnection as used by PUN in PhotonHandler.
   FIXED: [Editor only, Android] minor, warning in WebRtcAudioDsp custom editor won't be shown when AEC native setting of Photon microphone type is not used in Recorder.
   ADDED: [Editor only, Android] minor, warnings in WebRtcAudioDsp custom editor when AGC or NS native settings of Photon microphone type  are enabled in both places.
   FIXED: [Editor only] minor, avoiding sublists in dropdown of mic list in Editor when mic name contains '/'. (thanks Erick on twitch!)
   
v2.26.3 (July 28th, 2021)
   PUN2: v2.34.1 (92ac43d) [RT/lib:*******]
   PhotonVoiceApi: revision 6145
   FIXED: Recorder's OnApplicationPause and OnApplicationFocus handling: restarting or resetting audio happens only once.
   FIXED: [Android] proper audio device selection when using Photon microphone type.

v2.26.2 (July 07th, 2021)
   PUN2: v2.33.3 (fd4e0c5) [RT/lib:4.1.6.3]
   PhotonVoiceApi: revision 6126

v2.26.1 (July 07th, 2021)
   PUN2: v2.33.2 (63b058d) [RT/lib:4.1.6.3]
   PhotonVoiceApi: revision 6125
   FIXED: iOS native plugin (Photon microphone type) will now always auto reset audio session on routing change or interruptions.
   FIXED: [Editor only] parsing version info from changelog.txt.
   CHANGED: minor, some PhotonVoiceView warning logs turned into info logs.

v2.26 (June 29th, 2021)
   PUN2: v2.33.1 (c5f12a3) [RT/lib:4.1.6.3]
   PhotonVoiceApi: revision 6094
   ADDED: UWP as a supported platform for WebRtcAudioDsp component. (ARM64 flavour excluded as it will be added as part of HoloLens add-on later)
   FIXED: Old issue of Thai calendar format when generating log timestamp. (hopefully fixed for good this time)
   FIXED: Recorder.PhotonMicrophoneDeviceIdString setter in UWP. (inverted condition check due to missing '!')
   CHANGED: [Editor only] better traffic stats insights from VoiceConnection or PhotonVoiceNetwork custom inspectors.
   REMOVED: ForceToSpeaker iOS workaround files. Use Photon microphone type of built-in Unity option for this. See documentation here: https://doc.photonengine.com/en-us/voice/current/troubleshooting/known-issues#ios_speaker_issue
   REMOVED: Photon Realtime Wizard files.
   CHANGED: Recorder.SamplingRate will be set to 48kHz when WebRtcAudioDsp is used and requested value is 12kHz or 24kHz.
   CHANGED: Recorder.FrameDuration will be set to 10ms when WebRtcAudioDsp is used and requested value is 2.5ms or 5ms.
   CHANGED: [Editor only] Recorder.Bitrate is now IntSlider.
   CHANGED: [Editor only] Recorder microphones list hidden in edit mode.
   CHANGED: [Editor only] Recorder.SamplingRate and Recorder.FrameDuration are limited to WebRtcAudioDsp's supported values when the latter is used.
   ADDED: [VoicePunDemo] namespace to SoundsForJoinAndLeave.cs file.
   CHANGED: [VoicePunDemo] SoundsForJoinAndLeave.cs file: Player class explicit type set via using to avoid conflicts with other classes with the same name.
   CHANGED: [Editor only] WebRtcAudioDsp AEC warning showns only if Recorder uses Microphone as input source and Photon microphone type (previously we did not check for input source type).

v2.25.1 (June 9th, 2021)
   PUN2: v2.32 (bd38773) [RT/lib:4.1.6.1]
   PhotonVoiceApi: revision 6086

v2.25 (June 7th, 2021)
   PUN2: v2.31 (931c31a) [RT/lib:4.1.6.0]
   PhotonVoiceApi: revision 6086
   CHANGED: [BREAKING] Voice.AudioInEnumerator code changed, some methods and a property were removed.
   REMOVED: [BREAKING] obsolete method Recorder.Init(VoiceClient, Object).
   FIXED: Speaker.OnRemoteVoiceInfo (linking) return value (Initialized & either (should not start playback OR playbak started).
   ADDED: Recorder.PhotonMicrophoneDeviceIdString in UWP/WSA context as ID is string in this case and not int.
   ADDED: generic unified Recorder.MicrophoneDevice to get or set microphone device (using enumerator via Recorder.MicrophonesEnumerator) no matter the microphone type.
   CHANGED: Recorder.PhotonMicrophoneEnumerator is now obsolete, use the new generic unified non-static Recorder.MicrophonesEnumerator which works for all microphone types even Unity one.
   ADDED: Unity.AudioInEnumerator as Unity microphone type devices can now have its own enumerator (wrapper around UnityEnginer.Microphone.devices)
   ADDED: [Editor only] shortcut editor button from Recorder inspector to add WebRtcAudioDsp component if missing and supported.
   FIXED: [DemoVoiceUI] hide Photon mic toggle when platform does not support Photon mic type.
   FIXED: WebRtcAudioDsp.AEC was being ignored on iOS & Android.
   ADDED: support for native microphone capture (a.k.a Photon microphone type) for UWP/WSA via native plugin, it also supports microphone enumeration.
   CHANGED: WebRtcAudioDsp auto self disable (and log error) on platforms not supported. Inspector will show message if target platform is not supported.
   FIXED: minor, extra misleading debug log removed, OnApplicationFocus should call HandleApplicationPause and not OnApplicationPause.
   CHANGED: Recorder.AutoStart won't happen if recorder was previously stopped explicitly using Recorder.StopRecording().
   CHANGED: Recorder.AutoStart will cause recording to start if set to true and conditions are met.
   FIXED: warning due to auto start recording attempt when it's not expected.
   CHANGED: Speaker playback is now handled via Service calls inside VoiceConnection.Update for linked speakers only. (used to be via coroutine inside Speaker itself) This fixes playback when Speaker is not enabled or not active in the hierarchy and Speaker.PlaybackOnlyWhenEnabled is false.
   FIXED: inverted condition in Speaker.StartPlaying which could cause playback to not start.
   ADDED: [MAJOR CHANGE] Recorder.RecordOnlyWhenJoined and all necessary code. Default is true. The recorder's AutoStart will be 'delayed' so PUN integration (PhotonVoiceView has time to set recorder.UserData) and permission request work properly.
   FIXED: [DemoVoiceUI] potential NullReferenceException in RemoteSpeakerUI.OnDestroy.
   ADDED: [Editor only] VoiceConnection and PhotonVoiceNetwork inspector will display asset version info on top. Unfold it to get Photon Voice version, etc.
   CHANGED: [Editor only] extra logging parameter IgnoreGlobalLogLevel hidden by default in VoiceComponent inspectors, main built-in components show it via custom editors.
   CHANGED: [PUN Intregration] Voice client uses the same SerializationProtocol as PUN client.
   REMOVED: [Editor only] ExitPlayModeOnScriptCompile editor script as no longer needed in Unity 2018.x.
   REMOVED: unused third party utility script SavWav.cs.

v2.24.1 (April 13th, 2021)
   PUN2: v2.30 (341a087)
   PhotonVoiceApi: revision 6019
   FIXED: broken DemoVoicePun.
   ADDED: (back) opus Linux libs. Linux should be a supported platform.

v2.24 (March 15th, 2021)
   PUN2: v2.29 (fd2ef98)
   PhotonVoiceApi: revision 6019
   NOTE: [BREAKING] minimal supported Unity version is now 2018.4LTS.
   NOTE: [BREAKING] .netstandard2.0 Photon library is used. (See PUN2's changelog for more details)
   NOTE: [BREAKING] ProjectSettings' API Compatibility Level .NET4.x is required for the .netstandard2.0 library to be used. The old library to be used for .NET 3.5 is still in the project if you want to switch. (See PUN2's changelog for more details) 
   NOTE: [BREAKING] a client workaround used to connect to self-hosted Photon Server v4 is removed. You now need to explicitly set the LoadBalancingClient.SerializationProtocol to 1.6 before connecting to self-hosted Photon Server v4. v5 is OK.  (See PUN2's changelog for more details)
   FIXED: [Editor only] minor, two more CS0649 warnings.
   ADDED: [VoicePunDemo] a workaround to avoid NullReferenceException.
   CHANGED: [VoicePunDemo] CrossPlatformInput StandardAssets updated.
   ADDED: error log when PrimaryRecorder not set.
   FIXED: [VoiceDebugScript] IsMine should not be used as condition in CantHearYou RPC, comparing sender vs. receiver instead.
   
v2.23.2 (March 10th, 2021)
   PUN2: v2.28.3 (3c4b188)
   PhotonVoiceApi: revision 6004
   NOTE: [BREAKING] if you update from a version before 2.23, make sure to delete "Assets\Photon" root folder before importing. 
	    Otherwise you need to move (from Editor or from explorer by moving .meta files also) 
		"Assets\Photon\PhotonUnityNetworking\Code\Editor\AccountService.cs" and
		"Assets\Photon\PhotonUnityNetworking\Code\Editor\PhotonEditorUtils.cs" to respectively
		"Assets\Photon\PhotonRealtime\Code\Unity\Editor\AccountService.cs" and
		"Assets\Photon\PhotonRealtime\Code\Editor\PhotonEditorUtils.cs".
   ADDED: VoiceDebugScript to try to diagnose common issues in PUN integration
   FIXED: correct warning number pragma suppression (649 -> 414)
   FIXED: [DemoProximityVoiceChat] ChangeColour script to call RPC only if IsMine.
   FIXED: [DemoProximityVoiceChat] ChangeColour RPC is buffered.
   FIXED: [DemoProximityVoiceChat] ProximityVoiceTrigger checks if attached to local player and disable it if not.
   ADDED: debug logs when Recorder is destoryed or local voice removed.

v2.23.1 (February 25th, 2021)
   PUN2: v2.28.1 (433550f)
   PhotonVoiceApi: revision 5982

v2.23 (February 24th, 2021)
   PUN2: v2.28 (0a0ddf5)
   PhotonVoiceApi: revision 5982
   FIXED: Recorder.IsCurrentlyTransmitting in some cases starts true although it should be false.
   FIXED: Speaker.IsPlaying in some cases remains returns true although it should be false.
   FIXED: [Editor only] CS0414 warnings in Android or iOS context in Unity Editor.
   FIXED: CS649 warnings in for obsolete Speaker.playDelayMs and VoiceConnection.globalPlaybackDelay.
   FIXED: Speaker.Lag is -1 when Speaker is not playing.
   CHANGED: [Editor only] Recorder custom editor will recommend WebRtcAudioDsp.VAD instead of Recorder.VAD.
   CHANGED: Recorder.IsCurrentlyTransmitting property checks Recorder.IsRecording and Recorder.TransmitEnabled inside.
   FIXED: [DemoProximityVoiceChat] proximity voice chat's character pointer up status
   CHANGED: [DemoProximityVoiceChat] Recorder.TransmitEnabled disabled by default in scene for PrimaryRecorder
   CHANGED: making use of the new LoadBalancingClient.ClientType in LoadBalancingTransport and removing AppId switch workaround in VoiceConnection.ConnectUsingSettings.
   ADDED: DisallowMultipleComponents to VoiceConnection.
   CHANGED: Voice core is now using its own logger and log level decoupled from Realtime.
   CHANGED: [Editor only] Recorder.ReactOnSystemChanges & Recorder.SkipDeviceChangeChecks hidden from custom inspector when in Android or iOS.
   CHANGED: [Editor only] obsolete serialized fields hidden from inspector.
   CHANGED: [Editor only] allow ReactOnSystemChanges in Editor even if target platform is Android or iOS.

v2.22.3 (December 10th, 2020)
   PUN2: v2.26 (1bb4b17)
   PhotonVoiceApi: revision 5901
   CHANGED: skipping linking incoming remote streams with codec other than opus.

v2.22.2 (December 2nd, 2020)
   PUN2: v2.25.1 (cc3faf8)
   PhotonVoiceApi: revision 5901
   FIXED: [PhotonVoiceApi macOS, iOS] AudioInPusher: 'pushCallback' null check added in native callback to avoid NullReferenceException if SetCallback() gets called later than native input starts to produce samples.
   FIXED: [PhotonVoiceApi Windows] WindowsAudioInPusher.SetCallback(): 'pushCallback' is initialized last to make sure that null check in native callback guards all members.

v2.22.1 (November 29th, 2020)
   PUN2: v2.24 (b503239)
   PhotonVoiceApi: revision 5897
   FIXED: broken leftover code after renaming in Speaker.
   FIXED: [>=Unity 2019.1] incorrect method name in UiExtensions : InputField.SetTextWithoutNotify instead of InputField.SetValueWithoutNotify.
   FIXED: more CS649 warnings.
   CHANGED: fields used in Unity 2019.1 or higher wrapped in #ifdef.
   CHANGED: moved macOS voice libs under same folder "Assets\Photon\PhotonVoice\PhotonVoiceLibs\OSX".
   ADDED: PhotonVoiceNetwork private flags to support manual explicit voice client connect calls (e.g. ConnectUsingSettings).
   CHANGED: VoiceLogger timestamp format as an attempt to fix exception in Thai calendar/culture.
   REMOVED: minor, cleanup unused using in PhotonVoiceNetwork.

v2.22 (November 20th, 2020)
   PUN2: v2.23 (9c75048)
   PhotonVoiceApi: revision 5856
   FIXED: Speaker.StartPlayback should fail if Speaker is not enabled/active and PlaybackOnlyWhenEnabled is used.
   ADDED: [BREAKING] PhotonVoiceNetwork.UsePunAuthValues (default true): if used Photon Voice client will use the same AuthValues as PUN client. AuthMode and EncrytionMode are applied also now.
   ADDED: PlaybackDelaySettings which makes old PlayDelayMs/GlobalPlaybackDelay obsolete. Playback delay is now finetuned using 3 parameters. This should help fix the "high pitch" issue when player tries to catch up and does resampling.
   ADDED: Speaker.playbackExplicitlyStopped flag to make sure Speaker playback does not auto start again when not needed.
   CHANGED: NativeAndroidMicrophoneSettings to struct.
   ADDED: DisallowMultipleComponent attribute to Speaker, Recorder and WebRtcAudioDsp.
   ADDED: minor check to detect and log case where PUN & Voice clients are both connected to MasterServer.
   ADDED: [DemoVoiceUI] new AudioTone factory source to test playback delay.
   ADDED: [DemoVoiceUI] new playback delay settings.

v2.21 (September 8th, 2020)
   PUN2: v2.22 (9b9f51f5)
   PhotonVoiceApi: revision 5731
   CHANGED: DemoVoiceUI improved with more features, more insights and control over voice streams.
   CHANGED: some fields are now serialized no matter the platform to avoid unnecessary VCS changes when switching platforms.
   FIXED: IgnoreGlobalLogLevel taken into consideration when setting log level for Recorder or Speaker.
   CHANGED: Recorder.ReactOnSystemChanges ignored when Photon microphone type is used on Android or iOS as this is handled internally automatically via native plugins for the respective platforms.
   FIXED: 649 warnings for private SerializedFields not initialized.
   ADDED: Recorder.ResetLocalAudio to reset audio session and parameters locally to fix broken recording due to system configuration modifications or audio interruptions or audio routing changes. 
   CHANGED: Recorder.ResetLocalAudio is now used internally instead of or before Recorder.RestartRecording to try to automatically recover or resume recording.
   CHANGED: moved StopRecordingWhenPaused check in outer scope
   ADDED: Recorder.UseMicrophoneTypeFallback to optionally disable microphone type fallback in case of failures with initial type from Unity to Photon when applicable (and vice versa).
   FIXED: Recorder.subscribedToSystemChangesX flags are now properly reset when unsubscribed from callbacks.
   FIXED: when system callbacks fire, RequiresRestart is set only when mic device in use is no longer valid and do not it is not used as a check for this.
   ADDED: WebRtcAudioDsp.AecHighPass to be used with WebRtcAudioDsp.AEC.
   ADDED: WebRtcAudioDsp.AgcCompressionGain to be used with WebRtcAudioDsp.AGC, values are from 0 to 99. Default is 9.
   ADDED: checks & error logs when trying to assign Speaker.Actor for linked speakers.
   ADDED: [Experimental] WebRtcAudioDsp.ForceNormalAecInMobile to use regular non-mobile WebRTC AEC on Android or iOS.
   CHANGED: [PhotonVoiceApi] Android and iOS native audio input plugins updated to better handle routing changes, interruptions and make use of hardware audio processing features.

v2.20.1 (August 20th, 2020)
   PUN2: v2.21 (f42b1155)
   PhotonVoiceApi: revision 5698
   ADDED: [Logging] Info log entry when Speaker is about to start playback.
   ADDED: list of linked speakers in VoiceConnection.linkedSpeakers.
   CHANGED: setting VoiceConnection.GlobalPlaybackDelay will change Speaker.PlayDelayMs for linked speakers.
   CHANGED: setting VoiceConnection.GlobalSpeakersLogLevel will change Speaker.LogLevel for linked speakers only.

v2.20 (August 14th, 2020)
   PUN2: v2.20.2 (24c76182)
   PhotonVoiceApi: revision 5698
   CHANGED: delayed Speaker initialization until speaker is linked (once/the first time). This was necessary for PlayStation to be able to set UserID before playback start, but we chose to apply to all platforms.
   ADDED: [Logging] info log entries when subscribing and unsubscribing from audio config/device changes callbacks.
   FIXED: [PlayStation] Speaker.PlayStationUserID set before Speaker initialization. This fix is good unlike previous ones.
   FIXED: [Editor only] Recorder editor exception in WebGL context. UnityMicrophone.devices (also UnityMicrophone.IsRecording) now returns a value instead of thrown NotImplementedException.
   ADDED: [Experimental] Recorder.UseOnAudioFilterRead to make use of Unity's audio processing chain.
   REMOVED: Linux opus library (x86(_64)\libopus_egpv.so) files as it's not an officially supported platform.
   CHANGED: [PhotonVoiceApi] playback via UnityAudioOut enhanced to better handle network delay variations.

v2.19.1 (August 5th, 2020)
   PUN2: v2.20.2 (24c76182)
   PhotonVoiceApi: revision 5687
   FIXED: [PlayStation] setting UserID properly.

v2.19 (August 3rd, 2020)
   PUN2: v2.20.1 (6911d589)
   PhotonVoiceApi: revision 5685
   FIXED: Photon mic name logging, ID was passed instead of index.
   ADDED: fallback from Unity mic to Photon mic, the other way around exists already.
   FIXED: Recorder.RequiresRestart set only when really all necessary conditions are met.
   CHANGED: Android plugin for native microphone input updated to allow setting effects AEC, AGC and NS optionally & separately.
   ADDED: Native Android Microphone Settings when Photon mic type is used.
   ADDED: [Editor only] Best Region Box from PUN's ServerSettings to VoiceConnection's custom editor
   REMOVED: best region logic from VoiceConnection as we rely on LoadBalancingClient for this.
   CHANGED: VoiceConnection.ConnectUsingSettings relies on LoadBalancingClient's ConnectUsingSettings.
   FIXED: PhotonVoice connects to the same region as PUN when the integration is used.
   FIXED: Applied 'static' custom editor views when component is attached to prefab.
   ADDED: PhotonVoiceNetwork.WorkInOfflineMode.
   ADDED: UnityMicrophone instead of Microphone to compile for WebGL.
   FIXED: [PS4] auto set linked speakers' PS4UserID to the VoiceConnection's PS4UserID.
   FIXED: [DemoVoiceUI] show DSP settings in builds when enabled.
   ADDED: [Editor only] warning & detach button to VoiceConnection custom Editor when AppyDontDestroyOnLoad is enabled.
   FIXED: PhotonVoiceNetwork singleton pattern enforced (error logs, destroy extra, etc.).
   CHANGED: WebRTC dependency library updated to the latest. Wrapper and API adjusted accordingly.
   CHANGED: Playback enhanced when VAD is used.
   CHANGED: Recorder can change SamplingRate if it's not supported by WebRtcAudioDsp.

v2.18.3 (July 28th, 2020)
   PUN2: v2.19.3 (4b127d55)
   PhotonVoiceApi: revision 5639
   FIXED: plugins files that caused build failures on some platforms.

v2.18.2 (July 9th, 2020)
   PUN2: v2.19.3 (4b127d55)
   PhotonVoiceApi: revision 5639
   FIXED: broken WebRtcAudioDspEditor after removing WebRtcAudioDsp.aecMobile and WebRtcAudioDsp.aecMobileComfortNoise in 2.18.
   CHANGED: [Logging] some log calls levels from INFO to DEBUG
   FIXED: changing Recorder.SamplingRate when Recorder.TrySamplingRateMatch is false will now set Recorder.RequiresRestart as expected.
   CHANGED: finished returning hardcoded sampling rate per Photon mic plugin platform when Recorder.TrySamplingRateMatch is true.
   FIXED: plugin "Assets/Photon/PhotonVoice/PhotonVoiceLibs/x86/libopus_egpv.so" excluded from macOS builds.

v2.18.1 (July 8th, 2020)
   PUN2: v2.19.3 (4b127d55)
   PhotonVoiceApi: revision 5637
   FIXED: an error that caused the project to not compile in 2.18 on macOS or Linux.

v2.18 (July 8th, 2020)
   PUN2: v2.19.3 (4b127d55)
   PhotonVoiceApi: revision 5637
   ADDED: [Logging] debug logs when recorder starts, stops or restarts.
   CHANGED: [Logging] Voice PhotonPeer log level is now set early to make PhotonVoiceApi (Voice core, [PV]) logs work before connection attempt.
   CHANGED: [Logging] Voice PhotonPeer log level is now set to max log level between VoiceConnection.LogLevel & VoiceConnection.Settings.NetworkLogging. This is because PhotonVoiceApi (Voice core, [PV]) logs use the same log level as the networking peer log level. We may decouple in the future.
   CHANGED: [Logging] default log levels is now INFO instead of ERROR.
   CHANGED: [Logging] Voice components loggers will log timestamps.
   ADDED: VoiceConnection.GlobalPlaybackDelay to have a default PlaybackDelay for all Speaker components.
   CHANGED: Recorder.LoopAudioClip setter will not set RequiresRestart.
   ADDED: Recorder.Bitrate setter value checks: range is 6000-510000.
   CHANGED: VoiceConnection will ping regions only when OpGetRegion response is successful.
   CHANGED: [Logging] Recorder will log Error instead of Info when no Unity device mic is available. The log message was also improved.
   ADDED: PublishUserId option to ConnectAndJoin.
   ADDED: [Logging] error log when create room fails in ConnectAndJoin.
   CHANGED: ConnectAndJoin uses JoinRandomOrCreateRoom for faster matchmaking.
   FIXED: Voice can compile for WebGL without the demos. It's still not supported though.
   FIXED: RecorderEditor will not overwrite recorder.UnityMicrophoneDevice when Recorder inspector is selected. Always get Unity mic device index from recorder.
   ADDED: [Logging] global log levels per component type (Recorder / Speaker). Use IgnoreGlobalLogLevel to set a log level per component.
   CHANGED: [Logging] default voice component log levels will be reset to the new global default set in VoiceConnection/PhotonVoiceNetwork.
   CHANGED: [WebRtcAudioDsp] LogLevel follows Recorder unless IgnoreGlobalLogLevel is true.
   CHANGED: AutoCreateSpeakerIfNotFound moved from PhotonVoiceNetwork to VoiceConnection and used there in SimpleSpeakerFactory.
   CHANGED: checking if VoiceConnection.SpeakerPrefab has multiple speakers in VoiceConnection.SimpleSpeakerFactory and PhotonVoiceView.SetupSpeaker.
   CHANGED: [minor, breaking] we do not set spatialBlend to 1 anymore when Speaker is automatically created when AutoCreateSpeakerIfNotFound is true.
   ADDED: Recorder.RestartRecording(force) has a new param to force restart even if Recorder.RequiresRestart == false.
   CHANGED: [WebRtcAudioDsp] recording is restarted if WebRtcAudioDsp is added after recording is started.
   CHANGED: [WebRtcAudioDsp] dispose old processor if any before starting new one.
   CHANGED: [WebRtcAudioDsp] AEC Mobile obsolete: it should be automatically set when a mobile platform (Android or iOS) is detected.
   CHANGED: [WebRtcAudioDsp] AEC CN made obsolete.
   CHANGED: [Experimental] Speaker.PlayDelayMs can be adjusted on the fly at runtime.
   ADDED: [Experimental] USE_NEW_TRANSPORT custom define to make it easier to switch to a new breaking less-alloc transport layer in VoiceConnection.
   ADDED: [Experimental] Recorder.TrySamplingRateMatch to avoid resampling between encoder and microphone. Default is false.
   CHANGED: [PhotonVoiceApi] Opus FEC enabled and packet loss percentage (max expected) set to 30%.
   CHANGED: [PhotonVoiceApi iOS] audio session reinitialized on interruption end.
   CHANGED: [PhotonVoiceApi iOS] workaround added for AudioUnitInitialize() returning -66635 sometimes 

v2.17 (May 29th, 2020)
   PUN2: v2.18.1 (624dae8c)
   PhotonVoiceApi: revision 5558
   FIXED: [PhotonVoiceApi] macOS Photon mic enumerator to support all mic names strings & skip virtual/'ghost' devices.
   FIXED: [PhotonVoiceApi] UnityAudioOut clip play loop detection which was triggering "overrun" warning every ~5 seconds or so.
   ADDED: [PhotonVoiceView] warnings during setup about unexpected AudioSource settings.
   ADDED: [PhotonVoiceView] warnings during setup about Recorder settings.
   CHANGED: [PhotonVoiceView] log message level WARNING -> DEBUG in Setup when already setup.
   CHANGED: [PhotonVoiceView] refactored for a better clear setup of Recorder & Speaker.
   ADDED: [PhotonVoiceView] back Awake and attempt to init there.
   ADDED: [PhotonVoiceNetwork] attempt to setup PhotonVoiceView when receiving voice info.
   CHANGED: [PhotonVoiceView] using GetComponent instead of GetComponentInChildren for AudioSource as it is on the same GameObject as the Speaker.
   CHANGED: [PhotonVoiceNetwork.CheckLateLinking] refactored to accept Speaker directly.
   CHANGED: [PhotonVoiceNetwork.CheckLateLinking] 'UserData not ViewId': error log is now warning and better message.
   ADDED: [PhotonVoiceNetwork.CheckLateLinking] warning when received UserData does not match ViewId but ActorNumber match.
   FIXED: [DemoVoiceUI scene] proper UI update of speaker objects by adjusting previous RemoteSpeakerUI.cs changes.
   ADDED: [PunVoiceDemo] manual instantiation's custom local/remote prefabs.
   CHANGED: [PunVoiceDemo] CharacterInstantiation PUN demo script updated to be in sync w/ PUN 2's new OnJoinedInstantiate.
   FIXED: [PhotonVoiceView] correct way to tell if PhotonView is valid or not yet.
   FIXED: 2 CS0649 warnings: Field is never assigned by setting default value to false.
   FIXED: inconsistent EoL warning in on (re)complie/import.
   FIXED: [Editor only] minor, removing wrong useless code in RecorderEditor.RefreshPhotonMicrophoneDevices.
   FIXED: [Editor only] NullReferenceException when Photon mic name is null.
   CHANGED: [Editor only] minor, RecorderEditor.SetEnumIndex method is used in iOS Editor only.
   CHANGED: minor, consistency, usage of "this." enforced.

v2.16.1 (April 30th, 2020)
   PUN2: v2.18.1 (624dae8c)
   PhotonVoiceApi: revision 5464

v2.16 (April 29th, 2020)
   PUN2: v2.18 (8d47c57f)
   PhotonVoiceApi: revision 5464
   ADDED: Recorder.StopRecordingWhenPaused, default is false.
   ADDED: Recorder.RecordOnlyWhenEnabled and Speaker.PlaybackOnlyWhenEnabled both defaults are false.
   ADDED: Recorder.SkipDeviceChangeChecks to be used with Recorder.ReactOnSystemChanges. The default is false. Set to true to force restart in all cases.
   CHANGED: Speaker playback is optimized (AudioStreamPlayer no longer used) and runs on a separate coroutine.
   ADDED: Speaker playback methods: StartPlayback, StopPlayback and RestartPlayback.
   ADDED: Speaker.PlaybackStarted property (which is not the same as Speaker.IsPlaying).
   CHANGED: PhotonVoiceView will try Init in Awake and keeps retrying in Start which was changed to a coroutine.
   FIXED: harmless PhotonMicrophoneEnumerator error logs on iOS or Android.
   FIXED: Photon type microphone device validity checks in Recorder.
   CHANGED: PhotonVoiceStatsGui now shows actual voice frames stats.
   CHANGED: WebRtcAudioDsp can be used with all Recorder input source types but logs warning when not used with a microphone.
   FIXED: [DemoVoiceUI scene] Consistency between UI values and actual microphone type and device being used.
   ADDED: [DemoVoiceUI scene] Option to stream AudioClip.
   ADDED: [DemoVoiceUI scene] Option to toggle Photon VAD.
   ADDED: [DemoVoiceUI scene] Option to disable WebRTC DSP.
   FIXED: [DemoVoiceUI scene] Errors when the room name input field is used when the client is not joined nor connected.
   CHANGED: [Editor only] Runtime options are now hidden from the Recorder inspector when in play mode.
   CHANGED: [Editor only] AppSettings hidden from VoiceConnection/PhotonVoiceNetwork when the client is connected.
   CHANGED: [Editor only] UserData displayed in CachedRemoteVoiceInfos in VoiceConnection/PhotonVoiceNetwork inspector.
   
v2.15 (April 07th, 2020)
   PUN2: v2.17 (793fc726)
   PhotonVoiceApi: revision 5434
   FIXED: iOS crash when using Photon MicrophoneType which was introduced in v2.13 due to moving some logic to a worker thread.
   CHANGED: ReactOnSystemChanges now relies on a native plugin on iOS instead of Unity's AudioSettings.OnAudioConfigurationChanged.
   CHANGED: always refresh Recorder.PhotonMicrophoneEnumerator when ReactOnSystemChanges is enabled.
   ADDED: inspector tooltips when hovering over on some VoiceConnection fields shared with PUN's PhotonServerSettings.

v2.14 (March 30th, 2020)
   PUN2: v2.17 (a5c45f77)
   PhotonVoiceApi: revision 5350
   CHANGED: PunVoiceDemo now has AutoTransmit & VAD enabled by default.
   CHANGED: PunVoiceDemo now has AudioSource added to all prefabs with proper 3D Settings.
   CHANGED: PunVoiceDemo now has the ground plan/quad 10x bigger so characters don't fall off the edges quickly.
   CHANGED: PunVoiceDemo now has mesh colliders in trees so characters can't go through them.
   CHANGED: AppSettings are shown/unfolded by default in VoiceConnection.
   CHANGED: Recorder.UnityMicrophoneDevice name will hold the full device name instead of null for default device.
   CHANGED: transport protocol restrictions lifted, replaced with warning if not UDP.
   CHANGED: Recorder.ReactOnSystemChanges will restart recording only when the device in use is no longer available.

v2.13 (February 03rd, 2020)
   PUN2: v2.16 (ad87bf1e)
   PhotonVoiceApi: revision 5350
   FIXED: switching or rejoining rooms in PUN integration.
   FIXED: Recorder.InterestGroup returns used value in all cases.
   FIXED: iOS WebRTC library no longer contains 'main' symbol to not clash with other libraries.
   FIXED: IL2CPP support for Windows builds using Photon MicrophoneType.
   CHANGED: Recorder.DebugEchoMode forced to false when Recorder.InterestGroup != 0.
   CHANGED: prevent connection attempt when app is quitting.
   CHANGED: PhotonVoiceNetwork.JoinRoom returns bool.
   ADDED: error logs in case of join or leave failures by voice client.
   CHANGED: voice client will try to use same UserId as PUN when usePunAppSettings is true.
   FIXED: minor, avoiding exception when quitting in PUN integration demo.

v2.12 (November 26th, 2019)
   PUN2: v2.16 (ad87bf1e)
   PhotonVoiceApi: revision 5282
   FIXED: DivideByZeroException due to AudioClip.channels becoming zero in Unity.
   FIXED: voice not working when rejoining or switching rooms in voice clients.
   ADDED: extra safety checks and error logs when voice channels number is zero from sender or receiver end to avoid exceptions.
   CHANGED: Speaker component initializtion, setup and linking refactored for easier debugging.
   CHANGED: Recorder will not set RequiresRestart to true when 'overriding' default Unity microphone device string.
   ADDED: Unity Microphone helpers methods to Recorder.
   ADDED: new WebRtcAudioDspEditor warnings if component is disabled or Recorder does not use Microphone type.
   CHANGED: minor, Recorder caching moved to Awake from OnEnable.
   CHANGED: WebRtcAudioDsp now supports inactive AudioListener detection and can switch AudioListener used.
   FIXED: minor, channels number for AudioSpeakerMode.Prologic in WebRtcAudioDsp.
   FIXED: Type Short conversion check delayed in WebRtcAudioDsp.

v2.11 (October 11th, 2019)
   PUN2: v2.15 (11b52d6)
   PhotonVoiceApi: revision 5209
   FIXED: changes made to some voice components were not persisted on scene save due to custom editors.
   FIXED: ConnectAndJoin utility script's custom editor errors by removing leftover serialized property.
   CHANGED: [Better, easier Recorder initialization] Recorder.Init(VoiceClient, object) obsolete replaced with Recorder.Init(VoiceConnection).
   ADDED: [Better, easier Recorder initialization] VoiceConnection.InitRecorder(Recorder) method. 
   CHANGED: [Better, easier Recorder initialization] VoiceConnection.PrimaryRecorder is now automatically (internally/implicitly) initialized.
   CHANGED: Recorder.DebugEchoMode disabled when InterestGroup set to a value != 0.
   CHANGED: Recorder.AutoStart hidden in custom editor when Recorder is already initialized.
   CHANGED: always add PrimaryRecorder in ConnectAndJoin utility script if none found no matter the value of AutoTransmit.
   CHANGED: [Revised custom editor of WebRtcAudioDsp] better placement of warnings.
   CHANGED: [Revised custom editor of WebRtcAudioDsp] enabling "Bypass" hides other options & warnings.
   ADDED: info help box to custom editor about speaker prefab requiring Speaker component.

v2.10 (September 25th, 2019)
   PUN2: v2.15 (11b52d6)
   PhotonVoiceApi: revision 5209
   CHANGED: ConnectAndJoin utility script won't override AppVersion.
   CHANGED: VAD calibration now requires transmission to be enabled to work.
   FIXED: Usage of custom Settings with PUN integration.
   FIXED: MissingReferenceException when trying to destroy a Speaker that's already destroyed.
   ADDED: PhotonVoiceView now has a public Init method that you may call to force setup and initialization in case this did not happen automatically which is unexpected.
   FIXED: PhotonVoiceView setup and Recorder initialization with PUN integration that uses manual prefab instantiation by delaying first Init to Start from OnEnable.
   ADDED: New manual instantiation option in CharacterInstantiation utility script which can be tested in DemoVoicePun-Scene.
   FIXED: minor Android build issues when android:allowBackup and some other attributes or properties are used in the application's AndroidManifest.xml.
   ADDED: Option to dispatch incoming messages, even if Time.timeScale = 0: PhotonVoiceNetwork.MinimalTimeScaleToDispatchInFixedUpdate. It defaults to not being used. This idea is borrowed from PUN. See PhotonNetwork.MinimalTimeScaleToDispatchInFixedUpdate. Audio playback still does not work when Time.timeScale is 0.

v2.9 (August 16th, 2019)
   PUN2: v2.14 (7cb46fd)
   PhotonVoiceApi: revision 5193
   CHANGED: minimal Unity version from 5.6.6f2 to 2017.4.7f1
   ADDED: DemoProximityVoiceChat
   ADDED: MicAmplifier UtilityScript
   CHANGED: check for minimal connection settings before connecting
   CHANGED: WebRTC DSP AECMobileComfortNoise param exposed
   FIXED: support for 2019.2 (AudioSpeakerMode.Raw deprecated)

v2.8 (June 25th, 2019)
   PUN2: v2.13 (26fd056)
   PhotonVoiceApi: revision 5153
   CHANGED: PhotonVoiceNetwork.usePunAppSettings added to replace VoiceConnection.usePunSettings removed in previous version.
   FIXED: Recorder VAD properties.
   CHANGED: Photon mic. enum is now refreshed automatically on system changes.
   CHANGED: Recorder.SampleTypeConv obsolete, logic is now implicit.
   FIXED: Setting audio session category for iOS to Photon.Voice.IOS.AudioSessionCategory.MultiRoute.
   ADDED: "Leave a Review" menu shortcut.
   CHANGED: Recorder.VoiceDetectionThreshold is now a slider in the custom editor and not a float field.

v2.7 (May 17th, 2019)
   PUN2: v2.12 (8ea5f65)
   PhotonVoiceApi: revision 5102
   CHANGED: Recorder.PhotonVoiceCreatedParams obsolete.
   CHANGED: asmdef: PhotonUnityNetworking no longer referenced from PhotonVoice.
   CHANGED: asmdef: PhotonUnityNetworking no longer referenced from PhotonVoice.Editor.
   ADDED: asmdef: PhotonVoice.PUN and PhotonVoice.PUN.Editor. 
   CHANGED: VoiceConnection.usePunSettings and related custom editor code removed: use VoiceConnection.ConnectUsingSettings(settings) to pass PUN settings if needed.
   ADDED: PhotonVoiceEditorUtils and Editor shortcuts to remove PUN or Photon Chat. 
   CHANGED: iOS's audio initialization session parameters exposed in Editor and can be set using Recoder.SetIosAudioSessionParameters methods.
   CHANGED: changelog.txt renamed to changes-voice.txt.

v2.6.1 (April 30th, 2019)
   PUN2: v2.10 (c4cec86)
   PhotonVoiceApi: revision 5005
   FIXED: asmdef issue in VoiceConnection's custom Editor Script.

v2.6 (April 26th, 2019)
   PUN2: v2.10 (c4cec86)
   PhotonVoiceApi: revision 5005
   CHANGED: Recorder.ReInit renamed to Recorder.RestartRecording.
   CHANGED: Recorder.RequiresInit renamed to Recorder.RequiresRestart.
   CHANGED: IsInitialized is now only checking if Recorder.voiceClient is not null, other logic moved to Recorder.IsRecording.
   CHANGED: Recorder.RequiresRestart is always false when Recorder.IsRecording is false.
   CHANGED: Recorder.StartRecording and Recorder.StopRecording methods added.
   CHANGED: Recorder.AutoStart property added and exposed in custom editor.
   FIXED: Recorder.TypeConvert changes applied properly when recording is restarted.
   CHANGED: Recorder.Init should not be used to restart recording, use Recoder.RestartRecording instead.
   CHANGED: extra checks added for WebRtcAudioDsp (only Microphone supported, once disabled won't be reused)
   CHANGED: shortcut to PhotonServerSettings added to VoiceConnection custom editor
   CHANGED: warning removed from PhotonVoiceNetwork after using the newly renamed PascalCase ClientState values
   CHANGED: showing Photon peer traffic stats from VoiceConnection and PhotonVoiceNetwork editors
   CHANGED: shortcuts to documentation pages added from main Voice components (help icon)

v2.5.1 (April 3rd, 2019)
   PUN2: v2.9 (2211f0c)
   PhotonVoiceApi: revision 5005
   FIXED: ArgumentException on Speaker end by not starting stream when channels are 0 from Recorder end

v2.5 (March 11th, 2019)
   PUN2: v2.7 (1fa6aff)
   PhotonVoiceApi: revision 5005
   CHANGED: Recorder.IsRecording property added to stop and restart 'recording' after initialization.
   CHANGED: Recorder.ReactOnSystemChanges property added to reinit due to changes in system audio devices. Default is false for backward compatibility.
   FIXED: UI in DemoVoice scene, left sidebar should contain all info and gets updated if you change values in Editor.
   FIXED: WebRtcAudioDsp: proper mapping of AudioSettings.speakerMode to channels number.
   CHANGED: WebRtcAudioDsp: minor refactoring.
   FIXED: PhotonVoiceStatsGui, PhotonVoiceLagSimulationGui: return after auto disable components when no VoiceConnection is not found.
   FIXED: PhotonVoiceLagSimulationGui: VoiceClient.DebugLostPercent applied only when simulation is enabled.
   FIXED: VoiceConnection: Frames stats reset if not receiving any.
   FIXED: Recorder custom inspector: "Level" is shown only when recording and set to 0 when not transmitting.
   
v2.4.1 (February 15th, 2019)
   PUN2: v2.7 (1fa6aff)
   PhotonVoiceApi: revision 5005
       CHANGED: iOS native plugin initialization moved to separate thread to avoid 0.5 - 1 sec. pauses in main thread execution
   FIXED: issue that was breaking asset import
   NOTE: Unity 5.6.6f2 is now the minimum supported Unity version
   
v2.4 (February 15th, 2019)
   PUN2: v2.7 (1fa6aff)
   PhotonVoiceApi: revision 4981
   ADDED: Android 64-bit libraries (Unity 2017.4+)
   ADDED: PhotonVoiceStatsGui Utility Script
   ADDED: more debug info to VoiceConnection (and PhotonVoiceNetwork) custom Editor
   CHANGED: Recorder.AudioGroup obsolete, use Recorder.InterestGroup instead
   CHANGED: VoiceConnection.Client is now LoadBalancingTransport after renaming LoadBalancingFrontend
   FIXED: AEC Mobile couldn't be enabled in WebRtcAudioDsp
   CHANGED: allow PhotonVoiceNetwork to join a room with a custom name when AutoConnectAndJoin is disabled
   CHANGED: DemoVoiceUI scene now has UI properly initialized with correct values
   CHANGED: PhotonVoiceLagSimulationGui refactored to not clash with PhotonLagSimulationGui
   
v2.3 (January 25th, 2019)
    PUN2: v2.6 (578494a)
    PhotonVoiceApi: revision 4948
    FIXED: Speaker linking issues (debug echo toggling, disconnect & reconnect)

v2.2 (December 20th, 2018)
    PUN2: v2.6v (eeaa6a7)
    PhotonVoiceApi: revision 4948
    FIXED: self-hosted Photon Server support (Photon Voice client UserId if no set, will use the same UserId as PUN if available or a new GUID)
    ADDED: Nintendo Switch support (requires separate add-on)
    FIXED: 'late Speaker linking' (if a remote voice info event is received before PUN prefab instantiation, could happen during scene switching) 
    FIXED: AudioSource.spatialBlend is set to 1.0f only when Speaker/AudioSource are created automatically by PhotonVoiceView
    FIXED: log level filtering
    FIXED: VAD threshold calibration persists between Recorder sessions
    FIXED: PS4 integration after update to PhotonVoice2 (still require separate add-on)

v2.1.3 (December 7th, 2018)
    PUN2: v2.5 (4d59ede)
    PhotonVoiceApi: revision 4915
    FIXED: disconnects during delays due to scene loading
    FIXED: iOS crash due to native plugin files versions mismatch
    CHANGED: iOS audio output forced to speaker by default when using Photon microphone type
    
v2.1.2 (December 3rd, 2018)
    PUN2: v2.5 (4d59ede)
    PhotonVoiceApi: revision 4876
    FIXED: issue due to using Mic. name/Id from Editor in builds
    CHANGED: renamed Recorder.PhotonVoiceCreatedParams.AudioSource to Recorder.PhotonVoiceCreatedParams.AudioDesc 
    CHANGED: renamed VoiceAudioPreprocessor to WebRtcAudioDsp

v2.1.1 (November 21st, 2018)
    PUN2: v2.5 (4d59ede)
    PhotonVoiceApi: revision 4876 
    FIXED: Unity Editor freezing when using PUN 2 integration

v2.1 (November 8th, 2018)
    PUN2: v2.4.1v (14cb68f)
    PhotonVoiceApi: revision 4876 
    FIXED: PhotonVoiceNetwork fields can now be updated from the Unity Editor
    ADDED: Assembly Definitions for Photon Voice 2
    ADDED: Photon Chat as it was forgotten in initial release
    FIXED: Photon Voice 2 can connect to self-hosted Photon Server after setting SerializtionProtocol version to 1.6 in this case
    FIXED: NullRefException when trying to access Recorder.Logger before Recorder.Awake is called
    FIXED: PhotonVoiceView's Recorder setup when the ViewID changes if the Recorder is not part of the prefab
    FIXED: Errors or crashes when trying to setup local voice due to exceptions
    CHANGED: Recorder falls back to Unity microphone if Photon microphone setup encounter an error

v2.0 (October 10th, 2018)
    PUN2: v2.2.1 (3cd5540)
    PhotonVoiceApi: revision 4823
    Initial release of the new major Photon Voice version 2.
    Work-in-progress documentation: https://doc.photonengine.com/en-us/voice/v2
    PhotonVoice reference API: https://doc-api.photonengine.com/en/voice/v2
