using System.Collections.Generic;
using UnityEngine;
using System;
using System.IO;
using System.Text;

namespace VoxelPlay {

    public partial class VoxelPlayEnvironment : MonoBehaviour {

        void LoadGameBinaryFileFormat_16 (BinaryReader br, bool singleFile, bool preservePlayerPosition = false, VoxelDefinition fallbackVoxelDefinition = null) {
            // Character controller transform position & rotation
            Vector3 pos = DecodeVector3Binary(br);
            Vector3 characterRotationAngles = DecodeVector3Binary(br);
            Vector3 cameraLocalRotationAngles = DecodeVector3Binary(br);
            if (!preservePlayerPosition) {
                if ((UnityEngine.Object)characterController != null) {
                    characterController.MoveTo(pos);
                    characterController.transform.rotation = Quaternion.Euler(characterRotationAngles);
                    cameraMain.transform.localRotation = Quaternion.Euler(cameraLocalRotationAngles);
                    characterController.UpdateLook();
                }
            }
            stage = br.ReadInt16();
            string regionsIds = br.ReadString();
            if (singleFile) {
                LoadGameRegionBinaryFormat_16(br, fallbackVoxelDefinition);
                LoadGameExtraDataBinaryFormat(br);
            } else {
                if (!string.IsNullOrEmpty(regionsIds)) {
                    string[] regionIds = regionsIds.Split(',');
                    foreach (var regionId in regionIds) {
                        string filename = GetFullFilename(regionId);
                        if (!File.Exists(filename)) {
                            ShowError($"Region file {filename} not found");
                            continue;
                        }
                        using (BinaryReader brRegion = new BinaryReader(new FileStream(filename, FileMode.Open), Encoding.UTF8)) {
                            LoadGameRegionBinaryFormat_16(brRegion, fallbackVoxelDefinition);
                        }
                    }
                }

                {
                    string filename = GetFullFilename("extra");
                    using (BinaryReader brExtra = new BinaryReader(new FileStream(filename, FileMode.Open), Encoding.UTF8)) {
                        LoadGameExtraDataBinaryFormat(brExtra);
                    }
                }
            }
        }

        void LoadGameRegionBinaryFormat_16 (BinaryReader br, VoxelDefinition fallbackVoxelDefinition = null) {

            InitSaveGameStructs();

            // Read voxel definition table
            int vdCount = br.ReadInt16();
            for (int k = 0; k < vdCount; k++) {
                VoxelDefinition vd = GetVoxelDefinition(br.ReadString());
                if (VoxelDefinition.IsNull(vd) && fallbackVoxelDefinition != null) {
                    saveVoxelDefinitionsList.Add(fallbackVoxelDefinition);
                } else {
                    saveVoxelDefinitionsList.Add(vd);
                }
            }

            // Read item definition table
            int idCount = br.ReadInt16();
            for (int k = 0; k < idCount; k++) {
                saveItemDefinitionsList.Add(br.ReadString());
            }

            // Read chunks
            int numChunks = br.ReadInt32();
            VoxelDefinition voxelDefinition = defaultVoxel;
            int prevVdIndex = -1;
            Color32 voxelColor = Misc.color32White;
            for (int c = 0; c < numChunks; c++) {
                // Get chunk position
                Vector3d chunkPosition = DecodeVector3Binary(br).ToVector3d();
                VoxelChunk chunk = GetChunkUnpopulated(chunkPosition);
                byte isAboveSurface = br.ReadByte();
                chunk.isAboveSurface = isAboveSurface == 1;
                byte isPopulated = br.ReadByte();
                chunk.isPopulated = isPopulated == 1;
                chunk.allowTrees = false;
                chunk.modified = true;
                chunk.modifiedTimestamp = br.ReadInt16();
                chunk.voxelSignature = -1;
                chunk.renderState = ChunkRenderState.Pending;
                chunk.usesMicroVoxels = false;
                SetChunkOctreeIsDirty(chunkPosition, false);
                ChunkClearFast(chunk);

                // Read voxels
                int numWords = br.ReadInt16();
                for (int k = 0; k < numWords; k++) {
                    // Voxel definition
                    int vdIndex = br.ReadInt16();
                    if (prevVdIndex != vdIndex) {
                        if (vdIndex >= 0 && vdIndex < vdCount) {
                            voxelDefinition = saveVoxelDefinitionsList[vdIndex];
                            prevVdIndex = vdIndex;
                        }
                    }
                    // RGB
                    voxelColor.r = br.ReadByte();
                    voxelColor.g = br.ReadByte();
                    voxelColor.b = br.ReadByte();
                    // Voxel index
                    int voxelIndex = br.ReadInt16();
                    // Repetitions
                    int repetitions = br.ReadInt16();
                    // Flags (rotation and water level)
                    byte flags = br.ReadByte();

                    if (voxelDefinition == null) {
                        continue;
                    }

                    for (int i = 0; i < repetitions; i++) {
                        chunk.SetVoxel(voxelIndex + i, voxelDefinition, voxelColor);
                        chunk.voxels[voxelIndex + i].SetFlags(flags);
                    }
                }

                // Light sources
                int lightCount = br.ReadInt16();
                VoxelHitInfo hitInfo = new VoxelHitInfo();
                for (int k = 0; k < lightCount; k++) {
                    // Voxel index
                    hitInfo.voxelIndex = br.ReadInt16();
                    // Voxel center
                    hitInfo.voxelCenter = GetVoxelPosition(chunkPosition, hitInfo.voxelIndex);
                    // Normal
                    hitInfo.normal = DecodeVector3Binary(br);
                    hitInfo.chunk = chunk;
                    // Item definition
                    int itemIndex = br.ReadInt16();
                    if (itemIndex < 0 || itemIndex >= idCount)
                        continue;
                    string itemDefinitionName = saveItemDefinitionsList[itemIndex];
                    ItemDefinition itemDefinition = GetItemDefinition(itemDefinitionName);
                    TorchAttach(hitInfo, itemDefinition);
                }

                // Read items
                int itemCount = br.ReadInt16();
                for (int k = 0; k < itemCount; k++) {
                    // Voxel index
                    int itemIndex = br.ReadInt16();
                    if (itemIndex < 0 || itemIndex >= idCount)
                        continue;
                    string itemDefinitionName = saveItemDefinitionsList[itemIndex];
                    Vector3d itemPosition = DecodeVector3Binary(br).ToVector3d();
                    float quantity = br.ReadSingle();
                    ItemSpawn(itemDefinitionName, itemPosition, quantity);
                }

                // Load custom voxel properties
                if (chunk.voxelsProperties == null) {
                    chunk.voxelsProperties = new FastHashSet<FastHashSet<VoxelProperty>>();
                }
                int voxelsPropertiesCount = br.ReadInt16();
                for (int k = 0; k < voxelsPropertiesCount; k++) {
                    int voxelIndex = br.ReadInt16();
                    int voxelPropertiesCount = br.ReadInt16();
                    if (!chunk.voxelsProperties.TryGetValue(voxelIndex, out FastHashSet<VoxelProperty> voxelProperties)) {
                        voxelProperties = new FastHashSet<VoxelProperty>();
                        chunk.voxelsProperties[voxelIndex] = voxelProperties;
                    }
                    for (int i = 0; i < voxelPropertiesCount; i++) {
                        int propId = br.ReadInt32();
                        VoxelProperty prop;
                        prop.floatValue = br.ReadSingle();
                        prop.stringValue = br.ReadString();
                        voxelProperties[propId] = prop;
                    }
                }

                // Read microvoxels
                int mvCount = br.ReadInt16();
                if (mvCount > 0) {
                    if (chunk.microVoxels == null) {
                        chunk.microVoxels = new Dictionary<int, MicroVoxels>();
                    }
                    chunk.usesMicroVoxels = true;
                    for (int k = 0; k < mvCount; k++) {
                        int voxelIndex = br.ReadInt16();
                        MicroVoxels mv = new MicroVoxels();
                        mv.ReadFromBinaryReader(br);
                        if (voxelIndex >= 0) {
                            chunk.microVoxels[voxelIndex] = mv;
                            byte newOpaque = mv.GetOpaqueProportional();
                            chunk.voxels[voxelIndex].opaque = newOpaque;
                        }
                    }
                }
            }
        }

        void LoadGameExtraDataBinaryFormat (BinaryReader br) {
            // Destroy any object with VoxelPlaySaveThis component to avoid repetitions
            VoxelPlaySaveThis[] gos = Misc.FindObjectsOfType<VoxelPlaySaveThis>();
            for (int k = 0; k < gos.Length; k++) {
                DestroyImmediate(gos[k].gameObject);
            }

            // Load gameobjects
            int goCount = br.ReadInt16();
            Dictionary<string, string> data = new Dictionary<string, string>();
            for (int k = 0; k < goCount; k++) {
                string prefabPath = br.ReadString();
                string goName = br.ReadString();
                Vector3 goPosition = DecodeVector3Binary(br);
                Vector3 goAngles = DecodeVector3Binary(br);
                Vector3 goScale = DecodeVector3Binary(br);
                data.Clear();
                Int16 dataCount = br.ReadInt16();
                for (int j = 0; j < dataCount; j++) {
                    string key = br.ReadString();
                    string value = br.ReadString();
                    data[key] = value;
                }

                GameObject o = Resources.Load<GameObject>(prefabPath);
                if (o != null) {
                    o = Instantiate(o, goPosition, Quaternion.Euler(goAngles));
                    if (!o.TryGetComponent(out VoxelPlaySaveThis go)) {
                        DestroyImmediate(o);
                        continue;
                    }
                    o.name = goName;
                    o.transform.localScale = goScale;
                    go.SendMessage("OnLoadGame", data, SendMessageOptions.DontRequireReceiver);
                }
            }

            // Read number of custom sections
            int sectionsCount = br.ReadInt16();
            for (int k = 0; k < sectionsCount; k++) {
                string sectionName = br.ReadString();
                int length = br.ReadInt32();
                byte[] sectionData = br.ReadBytes(length);
                if (OnLoadCustomGameData != null) {
                    sectionData = SaveGameCustomDataWriter.Decompress(sectionData);
                    OnLoadCustomGameData(sectionName, sectionData);
                }
            }

        }


        void SaveGameBinaryFormat (BinaryWriter bw) {
            SaveGameHeaderBinaryFormat(bw, "");
            SaveGameChunksBinaryFormat(bw, GetChunks(ChunkModifiedFilter.OnlyModified));
            SaveGameExtraDataBinaryFormat(bw);
        }

        void SaveGameHeaderBinaryFormat (BinaryWriter bw, string regionIds) {

            // Header
            bw.Write(SAVE_FILE_CURRENT_FORMAT);
            bw.Write((byte)CHUNK_SIZE);
            // Character controller transform position
            if ((UnityEngine.Object)characterController != null) {
                EncodeVector3Binary(bw, characterController.transform.position);
                // Character controller transform rotation
                EncodeVector3Binary(bw, characterController.transform.rotation.eulerAngles);
            } else {
                EncodeVector3Binary(bw, Misc.vector3zero);
                EncodeVector3Binary(bw, Misc.vector3zero);
            }
            // Character controller's camera local rotation
            if (cameraMain != null) {
                EncodeVector3Binary(bw, cameraMain.transform.localRotation.eulerAngles);
            } else {
                EncodeVector3Binary(bw, Misc.vector3zero);
            }
            stage++;
            bw.Write((Int16)stage);
            bw.Write(regionIds);
        }

        void SaveGameExtraDataBinaryFormat (BinaryWriter bw) {

            // Add gameobjects
            VoxelPlaySaveThis[] gos = Misc.FindObjectsOfType<VoxelPlaySaveThis>();
            bw.Write((Int16)gos.Length);
            Dictionary<string, string> data = new Dictionary<string, string>();
            for (int k = 0; k < gos.Length; k++) {
                VoxelPlaySaveThis go = gos[k];
                if (string.IsNullOrEmpty(go.prefabResourcesPath)) {
                    go.prefabResourcesPath = "";
                }
                bw.Write(go.prefabResourcesPath);
                bw.Write(go.name);
                EncodeVector3Binary(bw, go.transform.position);
                EncodeVector3Binary(bw, go.transform.eulerAngles);
                EncodeVector3Binary(bw, go.transform.localScale);
                data.Clear();
                go.SendMessage("OnSaveGame", data, SendMessageOptions.DontRequireReceiver);
                Int16 dataCount = (Int16)data.Count;
                bw.Write(dataCount);
                foreach (KeyValuePair<string, string> entry in data) {
                    bw.Write(entry.Key);
                    bw.Write(entry.Value);
                }
            }

            // Custom sections
            SaveGameCustomDataWriter customDataWriter = new SaveGameCustomDataWriter();
            if (OnSaveCustomGameData != null) {
                OnSaveCustomGameData(customDataWriter);
            }
            customDataWriter.Flush(bw);
        }

        void SaveGameChunksBinaryFormat (BinaryWriter bw, List<VoxelChunk> chunks) {

            // Build a table with all voxel definitions used in modified chunks
            int voxelDefinitionsCount = 0;
            int itemDefinitionsCount = 0;
            int numChunks = 0;
            InitSaveGameStructs();

            // Pack used voxel and item definitions
            foreach (var chunk in chunks) {
                numChunks++;
                if (chunk.voxels != null) {
                    VoxelDefinition last = null;
                    for (int k = 0; k < chunk.voxels.Length; k++) {
                        VoxelDefinition vd = chunk.voxels[k].type;
                        if (vd == null || vd == last || vd.isDynamic || vd.doNotSave)
                            continue;
                        last = vd;
                        if (saveVoxelDefinitionsDict.TryAdd(vd, voxelDefinitionsCount)) {
                            saveVoxelDefinitionsList.Add(vd);
                            voxelDefinitionsCount++;
                        }
                    }
                }
                if (chunk.items != null) {
                    ItemDefinition last = null;
                    for (int k = 0; k < chunk.items.count; k++) {
                        Item item = chunk.items.values[k];
                        if (item == null)
                            continue;
                        ItemDefinition id = item.itemDefinition;
                        if (id == null || id == last)
                            continue;
                        last = id;
                        if (!saveItemDefinitionsDict.ContainsKey(id)) {
                            saveItemDefinitionsDict[id] = itemDefinitionsCount++;
                            saveItemDefinitionsList.Add(id.name);
                        }
                    }
                }
                if (chunk.lightSources != null) {
                    ItemDefinition last = null;
                    for (int k = 0; k < chunk.lightSources.Count; k++) {
                        ItemDefinition id = chunk.lightSources[k].itemDefinition;
                        if (id == null || id == last)
                            continue;
                        last = id;
                        if (!saveItemDefinitionsDict.ContainsKey(id)) {
                            saveItemDefinitionsDict[id] = itemDefinitionsCount++;
                            saveItemDefinitionsList.Add(id.name);
                        }
                    }
                }
            }

            // Add voxel definitions table
            int vdCount = saveVoxelDefinitionsList.Count;
            bw.Write((Int16)vdCount);
            for (int k = 0; k < vdCount; k++) {
                bw.Write(saveVoxelDefinitionsList[k].name);
            }

            // Add item definitions table
            int idCount = saveItemDefinitionsList.Count;
            bw.Write((Int16)idCount);
            for (int k = 0; k < idCount; k++) {
                bw.Write(saveItemDefinitionsList[k]);
            }

            // Add modified chunks
            bw.Write(numChunks);
            foreach (var chunk in chunks) {
                ToggleHiddenVoxels(chunk, true);
                WriteChunkData(bw, chunk);
                ToggleHiddenVoxels(chunk, false);
            }
        }

        void WriteChunkData (BinaryWriter bw, VoxelChunk chunk) {
            // Chunk position
            EncodeVector3Binary(bw, chunk.position.vector3);
            // Is above surface?
            bw.Write(chunk.isAboveSurface ? (byte)1 : (byte)0);
            // Is populated?
            bw.Write(chunk.isPopulated ? (byte)1 : (byte)0);
            // Modified timestamp
            bw.Write((Int16)chunk.modifiedTimestamp);

            int voxelDefinitionIndex = 0;
            VoxelDefinition prevVD = null;

            // Count voxels words
            int k = 0;
            int numWords = 0;
            while (k < chunk.voxels.Length) {
                if (chunk.voxels[k].typeIndex > 0) {
                    VoxelDefinition voxelDefinition = chunk.voxels[k].type;
                    if (voxelDefinition.isDynamic || voxelDefinition.doNotSave) {
                        k++;
                        continue;
                    }
                    if (voxelDefinition != prevVD) {
                        if (!saveVoxelDefinitionsDict.TryGetValue(voxelDefinition, out voxelDefinitionIndex)) {
                            k++;
                            continue;
                        }
                        prevVD = voxelDefinition;
                    }
                    Color32 tintColor = chunk.voxels[k].color;
                    int flags = chunk.voxels[k].GetFlags();
                    k++;
                    while (k < chunk.voxels.Length && chunk.voxels[k].type == voxelDefinition && chunk.voxels[k].color.r == tintColor.r && chunk.voxels[k].color.g == tintColor.g && chunk.voxels[k].color.b == tintColor.b && voxelDefinition.renderType != RenderType.Custom && chunk.voxels[k].GetFlags() == flags) {
                        k++;
                    }
                    numWords++;
                } else {
                    k++;
                }
            }
            bw.Write((Int16)numWords);

            // Write voxels
            k = 0;
            while (k < chunk.voxels.Length) {
                if (chunk.voxels[k].typeIndex > 0) {
                    int voxelIndex = k;
                    VoxelDefinition voxelDefinition = chunk.voxels[k].type;
                    if (voxelDefinition.isDynamic || voxelDefinition.doNotSave) {
                        k++;
                        continue;
                    }
                    if (voxelDefinition != prevVD) {
                        if (!saveVoxelDefinitionsDict.TryGetValue(voxelDefinition, out voxelDefinitionIndex)) {
                            k++;
                            continue;
                        }
                        prevVD = voxelDefinition;
                    }
                    Color32 tintColor = chunk.voxels[k].color;
                    byte flags = chunk.voxels[k].GetFlags();
                    int repetitions = 1;
                    k++;
                    while (k < chunk.voxels.Length && chunk.voxels[k].type == voxelDefinition && chunk.voxels[k].color.r == tintColor.r && chunk.voxels[k].color.g == tintColor.g && chunk.voxels[k].color.b == tintColor.b && voxelDefinition.renderType != RenderType.Custom && chunk.voxels[k].GetFlags() == flags) {
                        repetitions++;
                        k++;
                    }
                    bw.Write((Int16)voxelDefinitionIndex);
                    bw.Write(tintColor.r);
                    bw.Write(tintColor.g);
                    bw.Write(tintColor.b);
                    bw.Write((Int16)voxelIndex);
                    bw.Write((Int16)repetitions);
                    bw.Write(flags);
                } else {
                    k++;
                }
            }

            // Write light sources
            int lightCount = chunk.lightSources != null ? chunk.lightSources.Count : 0;
            bw.Write((Int16)lightCount);
            for (int j = 0; j < lightCount; j++) {
                LightSource lightSource = chunk.lightSources[j];
                int voxelIndex = lightSource.hitInfo.voxelIndex;
                Vector3 normal = lightSource.hitInfo.normal;
                int itemIndex = 0;
                ItemDefinition id = lightSource.itemDefinition;
                if (id != null) {
                    saveItemDefinitionsDict.TryGetValue(id, out itemIndex);
                }
                bw.Write((Int16)voxelIndex);
                EncodeVector3Binary(bw, normal);
                bw.Write((Int16)itemIndex);
            }

            // Write items
            int itemCount = chunk.items != null ? chunk.items.count : 0;
            bw.Write((Int16)itemCount);
            for (int j = 0; j < itemCount; j++) {
                Int16 itemIndex = 0;
                float itemQuantity = 0;
                Vector3 itemPosition = Misc.vector3zero;
                Item item = chunk.items.values[j];
                if (item != null && item.itemDefinition != null) {
                    ItemDefinition id = item.itemDefinition;
                    if (saveItemDefinitionsDict.TryGetValue(id, out int idIndex)) {
                        itemIndex = ((Int16)idIndex);
                        itemPosition = item.transform.position;
                        itemQuantity = item.quantity;
                    }
                }
                bw.Write(itemIndex);
                EncodeVector3Binary(bw, itemPosition);
                bw.Write(itemQuantity);
            }

            // Save custom voxel properties
            if (chunk.voxelsProperties != null) {
                List<KeyValuePair<int, FastHashSet<VoxelProperty>>> voxelsProperties = BufferPool<KeyValuePair<int, FastHashSet<VoxelProperty>>>.Get();
                List<KeyValuePair<int, VoxelProperty>> voxelProperties = BufferPool<KeyValuePair<int, VoxelProperty>>.Get();
                chunk.voxelsProperties.GetValues(voxelsProperties);
                int voxelsPropertiesCount = chunk.voxelsProperties.Count;
                bw.Write((Int16)voxelsPropertiesCount);
                for (int j = 0; j < voxelsPropertiesCount; j++) {
                    KeyValuePair<int, FastHashSet<VoxelProperty>> kvp = voxelsProperties[j];
                    bw.Write((Int16)kvp.Key); // voxel index

                    kvp.Value.GetValues(voxelProperties);
                    int voxelPropertiesCount = voxelProperties.Count;

                    bw.Write((Int16)voxelPropertiesCount); // properties count for this voxel
                    for (int i = 0; i < voxelPropertiesCount; i++) {
                        KeyValuePair<int, VoxelProperty> prop = voxelProperties[i];
                        bw.Write((Int32)prop.Key); // property id
                        bw.Write(prop.Value.floatValue); // float value
                        if (prop.Value.stringValue != null) {
                            bw.Write(prop.Value.stringValue); // string value
                        } else {
                            bw.Write("");
                        }
                    }
                }
                BufferPool<KeyValuePair<int, VoxelProperty>>.Release(voxelProperties);
                BufferPool<KeyValuePair<int, FastHashSet<VoxelProperty>>>.Release(voxelsProperties);
            } else {
                bw.Write((Int16)0);
            }

            // Write references to voxel meshes
            if (chunk.microVoxels != null) {
                bw.Write((Int16)chunk.microVoxels.Count);
                foreach (var mv in chunk.microVoxels) {
                    bw.Write((Int16)mv.Key);
                    mv.Value.WriteToBinaryWriter(bw);
                }
            } else {
                bw.Write((Int16)0);
            }

        }

    }
}

