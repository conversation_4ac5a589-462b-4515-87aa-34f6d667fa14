import base64
import json
import os
import copy

import requests

# OAuth2 token endpoint with placeholders for query parameters
TOKEN_URL = 'https://services.api.unity.com/auth/v1/token-exchange'

# Your service account credentials
SERVICE_ACCOUNT_ID = '083b1d77-c6b3-4011-bd18-b427876afeba'
SERVICE_ACCOUNT_SECRET = 'NtEGk01dcUvWkJLueJsblUXqj8d_xNzh'

# Economy Admin API details
PROJECT_ID = '2a3829d9-9214-4aa2-945f-c022527da06c'
ENV_PROD = '324e6458-1dfb-44f4-85a3-1704b9167822'
ENV_DEV = '720ca514-46e8-44ef-9e19-f4e58ed1c50a'
ENV_DEV_NAME = 'development'
ENV_PROD_NAME = 'production'

ENVIRONMENT_ID = ENV_DEV
ENVIRONMENT_NAME = ENV_DEV_NAME

credentials = f'{SERVICE_ACCOUNT_ID}:{SERVICE_ACCOUNT_SECRET}'
base64_credentials = base64.b64encode(credentials.encode()).decode()
headers = {
    'Authorization': f'Basic {base64_credentials}',
    'Content-Type': 'application/json'  # Correct content type for this endpoint
}


# Get OAuth2 token
def get_access_token():
    # Construct the URL with query parameters
    url = f'{TOKEN_URL}?projectId={PROJECT_ID}&environmentId={ENVIRONMENT_ID}'
    # Make the POST request
    response = requests.post(
        url,
        headers=headers
    )
    response_data = response.json()
    print('Access granted')
    print(response_data)
    return response_data.get('access_token')


# Fetch all inventory items
def fetch_inventory_items(environment_id=ENVIRONMENT_ID):
    response = requests.get(
        f'https://services.api.unity.com/economy/v2/projects/{PROJECT_ID}/environments/{environment_id}/configs/draft/resources',
        headers=headers
    )
    if response.status_code == 200:
        response_data = response.json()
        print(f'Items fetched, len = {len(response_data.get("results", []))}')
        return response_data
    else:
        print(f'Error fetching items: {response.status_code} - {response.text}')
        return None


# Delete inventory item by ID
def delete_inventory_item(resourceId):
    response = requests.delete(
        f'https://services.api.unity.com/economy/v2/projects/{PROJECT_ID}/environments/{ENVIRONMENT_ID}/configs/draft/resources/{resourceId}',
        headers=headers
    )

    print(f'Delete item response: {response}\n')
    return response.status_code


# Main function to remove all items
def remove_all_items():
    items = fetch_inventory_items()
    if items:
        for item in items.get('results', []):
            print(f'Fetched item, {item}')
            item_id = item['id']
            if item['type'] == 'CURRENCY':
                continue
            if item['type'] == 'INVENTORY_ITEM':
                continue
            status_code = delete_inventory_item(item_id)
            if status_code == 200:
                print(f'Successfully deleted item: {item_id}')
            else:
                print(f'Failed to delete item: {item_id}')

    items = fetch_inventory_items()
    if items:
        for item in items.get('results', []):
            print(f'Fetched item, {item}')
            item_id = item['id']
            status_code = delete_inventory_item(item_id)
            if status_code == 200:
                print(f'Successfully deleted item: {item_id}')
            else:
                print(f'Failed to delete item: {item_id}')


def post_inventory_item(new_item):
    json_to_post = {
        "id": new_item['id'],
        "type": "INVENTORY_ITEM",
        "name": new_item['name'],
        "customData": new_item['customData']
    }

    json_to_post['id'] = json_to_post['id'] + '_INV'

    add_item(json_to_post)

def edit_item(inv_item):
    response = requests.put(
        f'https://services.api.unity.com/economy/v2/projects/{PROJECT_ID}/environments/{ENVIRONMENT_ID}/configs/draft/resources/{inv_item['id']}',
        headers=headers,
        json=inv_item
    )
    print(f'Response: {response}\n')
    
def add_item(item):
    response = requests.post(
        f'https://services.api.unity.com/economy/v2/projects/{PROJECT_ID}/environments/{ENVIRONMENT_ID}/configs/draft/resources',
        headers=headers,
        json=item
    )
    print(f'Pushed new Item: {response.text}\n')    


def post_virtual_purchase_of_inventory(new_item, cost):
    json_to_post = {
        "id": new_item['id'],
        "type": "VIRTUAL_PURCHASE",
        "name": new_item['name']
    }

    if 'customData' in new_item and 'category' in new_item['customData'] and new_item['customData'][
        'category'] == 'in-app-purchase':
        if 'category' in new_item['customData']:
            if new_item['customData']['category'] == 'in-app-purchase':
                json_to_post['customData'] = {
                    "category": new_item['customData']['category'],
                    "subcategory": new_item['customData']['subcategory'],
                    "realCostUSD": new_item['customData']['realCostUSD']
                }
                json_to_post['rewards'] = [{"resourceId": "REMZ", "amount": cost}]
    else:
        json_to_post['rewards'] = [{"resourceId": new_item['id'] + '_INV', "amount": 1}]
        if cost > 0:
            json_to_post['costs'] = [{"resourceId": "REMZ", "amount": cost}]

    print(f'Item: {json_to_post}')

    # response = requests.post(
    #     f'https://services.api.unity.com/economy/v2/projects/{PROJECT_ID}/environments/{ENVIRONMENT_ID}/configs/draft/resources',
    #     headers=headers,
    #     json=json_to_post
    # )

    #  This is for editing a resource
    # response = requests.put(
    #     f'https://services.api.unity.com/economy/v2/projects/{PROJECT_ID}/environments/{ENVIRONMENT_ID}/configs/draft/resources/{json_to_post["id"]}',
    #     headers=headers,
    #     json=json_to_post
    # )
    # print(f'Item: {response.text}')


def list_player_inventory(oculusId, delete_all=False, delete_duplicates=False, ids_only=True):
    auth_url = 'https://player-auth.services.api.unity.com/v1/authentication/usernamepassword/sign-in'
    client_headers = {
        'Content-Type': 'application/json',
        'ProjectId': f'{PROJECT_ID}',
        'UnityEnvironment': f'{ENVIRONMENT_NAME}'
    }

    data = {
        "username": oculusId,
        "password": f'{oculusId}_Pw!'
    }

    auth_response = requests.post(auth_url, headers=client_headers, json=data)
    auth_json = auth_response.json()
    print(auth_json)
    if 'idToken' in auth_json:
        id_token = auth_json['idToken']
    else:
        return

    if 'user' not in auth_json:
        return

    playerId = auth_json['user']['id']
    inventory_url = f'https://economy.services.api.unity.com/v2/projects/{PROJECT_ID}/players/{playerId}/inventory?limit=100'
    inventory_headers = {
        'Authorization': f'Bearer {id_token}',
        'ProjectId': f'{PROJECT_ID}',
        'Content-Type': 'application/json'
    }
    inventory_response = requests.get(inventory_url, headers=inventory_headers)
    inv_items = []
    if 'results' in inventory_response.json():
        for item in inventory_response.json()['results']:
            print(f'item: {item}')
            if ids_only:
                inv_items.append(item['playersInventoryItemId'])
            else:
                inv_items.append(item)
    print(f'items: {(inv_items)}')
    if delete_all:
        for item in inv_items:
            delete_url = f'https://economy.services.api.unity.com/v2/projects/{PROJECT_ID}/players/{playerId}/inventory/{item}'
            delete_response = requests.delete(delete_url, headers=inventory_headers)
            print(f'delete: {delete_response}')

    duplicate_items = []
    if delete_duplicates:
        inv_item_ids = []
        if 'results' in inventory_response.json():
            for item in inventory_response.json()['results']:
                if item['inventoryItemId'] in inv_item_ids:
                    duplicate_items.append(item['playersInventoryItemId'])
                else:
                    inv_item_ids.append(item['inventoryItemId'])
                # print(f'item: {item}')

        print(f'duplicate items: {duplicate_items}')
        for item in duplicate_items:
            delete_url = f'https://economy.services.api.unity.com/v2/projects/{PROJECT_ID}/players/{playerId}/inventory/{item}'
            delete_response = requests.delete(delete_url, headers=inventory_headers)
            print(f'delete: {delete_response}')

    return inv_items


def sign_up(oculusId):
    auth_url = 'https://player-auth.services.api.unity.com/v1/authentication/usernamepassword/sign-up'
    client_headers = {
        'Content-Type': 'application/json',
        'ProjectId': f'{PROJECT_ID}',
        'UnityEnvironment': f'{ENVIRONMENT_NAME}'
    }

    data = {
        "username": oculusId,
        "password": f'{oculusId}_Pw!'
    }

    auth_response = requests.post(auth_url, headers=client_headers, json=data)
    auth_json = auth_response.json()
    print(auth_json)


def add_player_inventory(oculus_id, item):
    auth_url = 'https://player-auth.services.api.unity.com/v1/authentication/usernamepassword/sign-in'
    client_headers = {
        'Content-Type': 'application/json',
        'ProjectId': f'{PROJECT_ID}',
        'UnityEnvironment': f'{ENVIRONMENT_NAME}'
    }

    data = {
        "username": oculus_id,
        "password": f'{oculus_id}_Pw!'
    }

    auth_response = requests.post(auth_url, headers=client_headers, json=data)

    if 'idToken' in auth_response.json():
        id_token = auth_response.json()['idToken']
    else:
        return

    if 'user' not in auth_response.json() or 'id' not in auth_response.json()['user']:
        return

    playerId = auth_response.json()['user']['id']

    inventory_url = f'https://economy.services.api.unity.com/v2/projects/{PROJECT_ID}/players/{playerId}/inventory'
    inventory_headers = {
        'Authorization': f'Bearer {id_token}',
        'ProjectId': f'{PROJECT_ID}',
        'Content-Type': 'application/json'
    }
    payload = {
        'inventoryItemId': item
    }
    print(f'payload: {payload}')
    inventory_response = requests.post(inventory_url, headers=inventory_headers, json=payload)
    print(f'items: {inventory_response.json()}')


def remove_player_inventory(oculus_id, inventory_item_id):
    inv_items = list_player_inventory(oculus_id, ids_only=False)

    item_to_remove = None
    for item in inv_items:
        if item['inventoryItemId'] == inventory_item_id:
            item_to_remove = item
            break

    if item_to_remove is None:
        return

    auth_url = 'https://player-auth.services.api.unity.com/v1/authentication/usernamepassword/sign-in'
    client_headers = {
        'Content-Type': 'application/json',
        'ProjectId': f'{PROJECT_ID}',
        'UnityEnvironment': f'{ENVIRONMENT_NAME}'
    }

    data = {
        "username": oculus_id,
        "password": f'{oculus_id}_Pw!'
    }

    auth_response = requests.post(auth_url, headers=client_headers, json=data)

    if 'idToken' in auth_response.json():
        id_token = auth_response.json()['idToken']
    else:
        return

    if 'user' not in auth_response.json() or 'id' not in auth_response.json()['user']:
        return

    playerId = auth_response.json()['user']['id']

    inventory_item_id = item_to_remove['playersInventoryItemId']
    inventory_url = f'https://economy.services.api.unity.com/v2/projects/{PROJECT_ID}/players/{playerId}/inventory/{inventory_item_id}'
    inventory_headers = {
        'Authorization': f'Bearer {id_token}',
        'ProjectId': f'{PROJECT_ID}',
        'Content-Type': 'application/json'
    }
    inventory_response = requests.delete(inventory_url, headers=inventory_headers)
    print(f'items: {inventory_response}')


def list_all_economy_resources(item_type='ALL'):
    url = f'https://services.api.unity.com/economy/v2/projects/{PROJECT_ID}/environments/{ENVIRONMENT_ID}/configs/published/resources'
    response = requests.get(url, headers=headers)
    items = response.json()['results']
    if item_type != 'ALL':
        items = [item for item in items if item['type'] == item_type]
        
    # for i in items:
    #     print(f'{i["id"]}: {i["name"]}')
    return items
def change_user_balance(user_oculus_id, balance_change):
    auth_json = log_in_ugs(user_oculus_id)
    print(auth_json)
    if auth_json is None:
        return {}

    if 'idToken' in auth_json:
        id_token = auth_json['idToken']
    else:
        return {}

    if 'user' not in auth_json or 'id' not in auth_json['user']:
        return {}
    playerId = auth_json['user']['id']

    request_headers = {
        'Authorization': f'Bearer {id_token}',
        'ProjectId': f'{PROJECT_ID}',
        'Content-Type': 'application/json'
    }

    request_url = f'https://economy.services.api.unity.com/v2/projects/{PROJECT_ID}/players/{playerId}/currencies/REMZ'
    if balance_change < 0:
        request_url += '/decrement'
    else:
        request_url += '/increment'

    payload = {
        'amount': abs(balance_change)
    }

    try:
        inventory_response = requests.post(request_url, headers=request_headers, json=payload)
        print(inventory_response.json())
    except Exception as e:
        logger.error('Could not update UGS balance', user_id=user_id, error=str(e))


def log_in_ugs(user_oculus_id):
    auth_url = 'https://player-auth.services.api.unity.com/v1/authentication/usernamepassword/sign-in'
    client_headers = {
        'Content-Type': 'application/json',
        'ProjectId': f'{PROJECT_ID}',
        'UnityEnvironment': f'{ENVIRONMENT_NAME}'
    }

    data = {
        "username": user_oculus_id,
        "password": f'{user_oculus_id}_Pw!'
    }
    try:
        auth_res = requests.post(auth_url, headers=client_headers, json=data)
        print(auth_res)
        return auth_res.json()
    except Exception as e:
        logger.error('Could not log in to UGS', user_oculus_id=user_oculus_id, error=str(e))
        return None

def push_dev_inventory_to_prod(print_only=False):
    dev_items = fetch_inventory_items(ENV_DEV)
    prod_items = fetch_inventory_items(ENV_PROD)

    new_items = []
    # compare the two sets of inventory
    for dev_item in dev_items.get('results', []):
        found = False
        for prod_item in prod_items.get('results', []):
            if dev_item['id'] == prod_item['id']:
                found = True
                break
        if not found:
            new_items.append(dev_item)

    print(f'New items: {len(new_items)}\n {new_items}')

    if print_only:
        return

    virtual_purchases = []
    inventory = []
    currency = []
    for item in new_items:
        if item['type'] == 'VIRTUAL_PURCHASE':
            virtual_purchases.append(item)
        if item['type'] == 'INVENTORY_ITEM':
            inventory.append(item)
        if item['type'] == 'CURRENCY':
            currency.append(item)

    pushed_items = []
    for item in currency:
        print(f'Fetched item, {item}')
        print()
        response = requests.post(
            f'https://services.api.unity.com/economy/v2/projects/{PROJECT_ID}/environments/{ENV_PROD}/configs/draft/resources',
            headers=headers,
            json=item
        )
        print(f'Post result: {response.status_code} {response.text}')
        print()
        print()
        if response.status_code == 201:
            pushed_items.append(item)

    for item in inventory:
        print(f'Fetched item, {item}')
        print()
        response = requests.post(
            f'https://services.api.unity.com/economy/v2/projects/{PROJECT_ID}/environments/{ENV_PROD}/configs/draft/resources',
            headers=headers,
            json=item
        )
        print(f'Post result: {response.status_code} {response.text}')
        print()
        print()
        if response.status_code == 201:
            pushed_items.append(item)

    for item in virtual_purchases:
        print(f'Fetched item, {item}')
        response = requests.post(
            f'https://services.api.unity.com/economy/v2/projects/{PROJECT_ID}/environments/{ENV_PROD}/configs/draft/resources',
            headers=headers,
            json=item
        )
        print(f'Post result: {response.status_code} {response.text}')
        print()
        print()
        if response.status_code == 201:
            pushed_items.append(item)

    print(f'Pushed items: {len(pushed_items)}\n {pushed_items}')
    
def bulk_edit_costs_and_levels(edits, print_only=False):
    all_inventory = list_all_economy_resources()

    changed_inventory = []
    for edit in edits:
        for item in all_inventory:
            if str.upper(edit['id']) == item['id'] and item['type'] == 'VIRTUAL_PURCHASE':
                if len(item['costs']) == 0 and edit['amount'] > 0:
                    item['costs'].append({'resourceId': 'REMZ', 'amount': edit['amount']})
                    changed_inventory.append(item)
                    print(f"added because no cost exixted")
                elif len(item['costs']) > 0:
                    for cost in item['costs']:
                        if cost['resourceId'] == 'REMZ':
                            if cost['amount'] != edit['amount']:
                                cost['amount'] = edit['amount']
                                changed_inventory.append(item)
                                print(f"added because cost existed but wrong")
            
            if str.upper(edit['id']+'_INV') == item['id'] and item['type'] == 'INVENTORY_ITEM':
                if 'level' in item['customData'] and item['customData']['level'] != edit['level']:
                    item['customData']['level'] = edit['level']
                    changed_inventory.append(item)
                elif 'level' not in item['customData']:
                    item['customData']['level'] = edit['level']
                    changed_inventory.append(item)

    for item in changed_inventory:
        print(f'Changed item, {item}')
        print()
        if not print_only:
            edit_item(item)

def bulk_add_new_inventory_items(inventory, print_only=False):
    all_inventory = list_all_economy_resources()

    new_inventory = []
    for inventory_item in inventory:
        exists = False
        for item in all_inventory:
            if str.upper(inventory_item['id']+'_INV') == item['id']:
                exists = True
                break
        if not exists:  
            new_inventory.append(inventory_item)

    inventory_to_push = []
    for item in new_inventory:
        new_item = {
            'id': str.upper(item['id'])+'_INV',
            'name': item['name'],
            'type': 'INVENTORY_ITEM',
            'customData': {
                'type': item['type'],
            }
        }
        if 'level' in item:
            new_item['customData']['level'] = item['level']

        inventory_to_push.append(new_item)
        
    for item in inventory_to_push:
        print(f'New item, {item}')
        if not print_only:
            add_item(item)

def bulk_add_type_to_inventory(edits = [], print_only=False):
    all_inventory = list_all_economy_resources()

    changed_inventory = []
    for edit in edits:
        for item in all_inventory:
            if str.upper(edit['id']+'_INV') == item['id'] and item['type'] == 'INVENTORY_ITEM':
                if 'type' in item['customData'] and item['customData']['type'] != edit['type']:
                    item['customData']['type'] = edit['type']
                    changed_inventory.append(item)
                elif 'type' not in item['customData']:
                    item['customData']['type'] = edit['type']
                    changed_inventory.append(item)


    for item in changed_inventory:
        print(f'Changed item, {item}')
        print()
        if not print_only:
            edit_item(item)

def bulk_remove_inv_from_inv_name(print_only=False):
    all_inventory = list_all_economy_resources()

    changed_inventory = []
    for item in all_inventory:
        if item['type'] == 'INVENTORY_ITEM' and str.endswith(item['name'], ' Inv'):
            item['name'] = str.replace(item['name'], ' Inv', '')
            changed_inventory.append(item)

    for item in changed_inventory:
        print(f'Changed item, {item}')
        if not print_only:
            edit_item(item)

def bulk_edit_name(current, new, print_only=False):
    all_inventory = list_all_economy_resources()

    changed_inventory = []
    for item in all_inventory:
        new_string = str.replace(item['name'], current, new)
        if new_string != item['name']:
            item['name'] = new_string
            changed_inventory.append(item)

    for item in changed_inventory:
        print(f'Changed item, {item}')
        if not print_only:
            edit_item(item)

def bulk_add_new_virtual_purchases(virtual_purchases, inventory, print_only=False):
    all_inventory = list_all_economy_resources()

    new_purchases = []
    for virtual_purchase in virtual_purchases:
        purchase_exists = False
        for item in all_inventory:
            if str.upper(virtual_purchase['id']) == item['id'] and item['type'] == 'VIRTUAL_PURCHASE':
                purchase_exists = True
                break

        if purchase_exists:
            continue
        new_purchase = {
            'id': str.upper(virtual_purchase['id']),
            'name': virtual_purchase['name'],
            'type': 'VIRTUAL_PURCHASE',
        }
        if 'amount' in virtual_purchase and virtual_purchase['amount'] > 0:
            new_purchase['costs'] = [{'resourceId': 'REMZ', 'amount': virtual_purchase['amount']}]

        rewards = []
        if 'childKey' in virtual_purchase and virtual_purchase.get('isParent', None) == 'TRUE':
            for inv in inventory:
                if inv.get('childKey', '') == virtual_purchase['childKey'] and inv['id'] != virtual_purchase['id'] and inv['type'] != 'bundle':
                    rewards.append({"resourceId": str.upper(inv['id']) + '_INV', "amount": 1})
        elif virtual_purchase['type'] == 'simple':
            rewards = [{'resourceId': str.upper(virtual_purchase['id']) + '_INV', 'amount': 1}]
        
        if 'bundleCoins' in virtual_purchase:
            rewards.append({'resourceId': 'REMZ', 'amount': virtual_purchase['bundleCoins']})
        if len(rewards) > 0:
            new_purchase['rewards'] = rewards

        new_purchase['customData'] = {}
        new_purchase['customData']['type'] = virtual_purchase['type']

        if 'description' in virtual_purchase:
            new_purchase['customData']['description'] = virtual_purchase['description']

        if 'availableEnd' in virtual_purchase:
            new_purchase['customData']['availableEnd'] = virtual_purchase['availableEnd']

        if 'availableStart' in virtual_purchase:
            new_purchase['customData']['availableStart'] = virtual_purchase['availableStart']

        if 'realCostUSD' in virtual_purchase:
            new_purchase['customData']['realCostUSD'] = virtual_purchase['realCostUSD']

        if 'class' in virtual_purchase:
            new_purchase['customData']['class'] = virtual_purchase['class']

        if 'isLocked' in virtual_purchase:
            new_purchase['customData']['isLocked'] = virtual_purchase['isLocked'] == 'TRUE'

        if 'group' in virtual_purchase:
            new_purchase['customData']['group'] = virtual_purchase['group']
            
        new_purchases.append(new_purchase)

    for item in new_purchases:
        print(f'new item to add, {item}')
        if not print_only:
            add_item(item)

def bulk_add_custom_data_to_existing_virtual_purchases(virtual_purchases, print_only=False):
    all_inventory = list_all_economy_resources()

    changed_purchases = []
    new_purchases = []
    
    for item in all_inventory:
        if item['type'] != 'VIRTUAL_PURCHASE':
            continue
        
        original_item = copy.deepcopy(item)
        for virtual_purchase in virtual_purchases:
            if str.upper(virtual_purchase['id']) == item['id']:
                changed = False
                if 'customData' not in item or item['customData'] is None:
                    item['customData'] = {}
                    changed = True
                    
                if 'type' not in item['customData'] or ('type' in item['customData'] and item['customData']['type'] != virtual_purchase['type']):
                    print(f'changed type from {item.get("customData", {}).get("type", None)} to {virtual_purchase["type"]}')
                    item['customData']['type'] = virtual_purchase['type']
                    changed = True

                if 'description' in virtual_purchase and item['customData'].get('description', None) != virtual_purchase['description']:
                    print(f'changed description from {item.get("customData", {}).get("description", None)} to {virtual_purchase["description"]}')
                    item['customData']['description'] = virtual_purchase['description']
                    changed = True

                if 'availableEnd' in virtual_purchase and item['customData'].get('availableEnd', None) != virtual_purchase['availableEnd']:
                    print(f'changed availableEnd from {item.get("customData", {}).get("availableEnd", None)} to {virtual_purchase["availableEnd"]}')
                    item['customData']['availableEnd'] = virtual_purchase['availableEnd']
                    changed = True

                if 'availableStart' in virtual_purchase and item['customData'].get('availableStart', None) != virtual_purchase['availableStart']:
                    print(f'changed availableStart from {item.get("customData", {}).get("availableStart", None)} to {virtual_purchase["availableStart"]}')
                    item['customData']['availableStart'] = virtual_purchase['availableStart']
                    changed = True
                    
                if 'realCostUSD' in virtual_purchase and item['customData'].get('realCostUSD', None) != virtual_purchase['realCostUSD']:
                    print(f'changed realCostUSD from {item.get("customData", {}).get("realCostUSD", None)} to {virtual_purchase["realCostUSD"]}')
                    item['customData']['realCostUSD'] = virtual_purchase['realCostUSD']
                    changed = True

                if 'class' in virtual_purchase and item['customData'].get('class', None) != virtual_purchase['class']:
                    print(f'changed class from {item.get("customData", {}).get("class", None)} to {virtual_purchase["class"]}')
                    item['customData']['class'] = virtual_purchase['class']
                    changed = True

                if 'group' in virtual_purchase and item['customData'].get('group', None) != virtual_purchase['group']:
                    print(f'changed group from {item.get("customData", {}).get("group", None)} to {virtual_purchase["group"]}')
                    item['customData']['group'] = virtual_purchase['group']
                    changed = True

                if 'isLocked' in virtual_purchase and item['customData'].get('isLocked', None) != (virtual_purchase['isLocked'] == 'TRUE'):
                    print(f'changed isLocked from {item.get("customData", {}).get("isLocked", None)} to {(virtual_purchase['isLocked'] == 'TRUE')}')
                    item['customData']['isLocked'] = virtual_purchase['isLocked'] == 'TRUE'
                    changed = True

                if not changed:
                    continue
                    
                print(f'Original item, {original_item}')
                print(f'Changed item, {item}')
                print()
                changed_purchases.append(item)
                break
    
    print(f'Changed items, {len(changed_purchases)}')
    for item in changed_purchases:
        if not print_only:
            edit_item(item)

def bulk_edit_existing_virtual_purchase_rewards(virtual_purchases, inventory, print_only=False):
    all_inventory = list_all_economy_resources()

    changed_purchases = []
    new_purchases = []

    for item in all_inventory:
        if item['type'] != 'VIRTUAL_PURCHASE':
            continue

        original_item = copy.deepcopy(item)
        for virtual_purchase in virtual_purchases:
            if item['id'] != str.upper(virtual_purchase['id']):
                continue
            if 'childKey' in virtual_purchase and virtual_purchase.get('isParent', None) == 'TRUE':
                rewards = []
                for inv in inventory:
                    if inv.get('childKey', '') == virtual_purchase['childKey'] and inv.get('isParent', None) is None:
                        rewards.append({"resourceId": str.upper(inv['id']) + '_INV', "amount": 1})
                if 'bundleCoins' in virtual_purchase:
                    rewards.append({'resourceId': 'REMZ', 'amount': virtual_purchase['bundleCoins']})
                if len(item.get('rewards', [])) != len(rewards):
                    item['rewards'] = rewards
                    print(f'changed = {item}\n')
                    changed_purchases.append(item)

    print(f'Changed items, {len(changed_purchases)}')
    for item in changed_purchases:
        if not print_only:
            edit_item(item)

def bulk_remove_unmatched_virtual_purchases(virtual_purchases, print_only=False):
    all_inventory = list_all_economy_resources()

    changed_purchases = []
    for item in all_inventory:
        if item['type'] != 'VIRTUAL_PURCHASE':
            continue
        
        found = False
        for virtual_purchase in virtual_purchases:
            if item['id'] == str.upper(virtual_purchase['id']):
                found = True
                break
        if not found:
            changed_purchases.append(item)

    print(f'Changed items, {len(changed_purchases)}\n')
    for item in changed_purchases:
        print(f'item to remove {item}')
        if not print_only:
            delete_inventory_item(item['id'])

def bulk_remove_old_virtual_purchase_data(print_only=False):
    all_inventory = list_all_economy_resources()

    changed_purchases = []
    for item in all_inventory:
        if item['type'] != 'VIRTUAL_PURCHASE':
            continue
        
        changed = False
        if 'customData' not in item:
            continue
            
        if 'category' in item['customData']:
            item['customData'].pop('category', None)
            changed = True
        if 'subcategory' in item['customData']:
            item['customData'].pop('subcategory', None)
            changed = True
        if 'unlockConditions' in item['customData']:
            item['customData'].pop('unlockConditions', None)
            changed = True
        if 'source' in item['customData']:
            item['customData'].pop('source', None)
            changed = True
            
        if changed:
            changed_purchases.append(item)

    print(f'Changed items, {len(changed_purchases)}\n')
    for item in changed_purchases:
        print(f'item to edit {item}')
        if not print_only:
            edit_item(item)

def bulk_remove_custom_data_from_inventory(inventory, print_only=False):
    all_inventory = list_all_economy_resources()

    changed_purchases = []
    new_purchases = []

    for item in all_inventory:
        if item['type'] != 'INVENTORY_ITEM':
            continue

        original_item = copy.deepcopy(item)
        for inventory_item in inventory:
            if str.upper(inventory_item['id'] + '_INV') == item['id']:
                changed = False
                if 'customData' not in item or item['customData'] is None:
                    continue

                if item['customData'].get('description', None) is not None:
                    item['customData'].pop('description', None)
                    changed = True
                    
                if item['customData'].get('unlockConditions', None) is not None:
                    item['customData'].pop('unlockConditions', None)
                    changed = True
                    
                if item['customData'].get('unlockKey', None) is not None:
                    item['customData'].pop('unlockKey', None)
                    changed = True
                    
                if item['customData'].get('availableEnd', None) is not None:
                    item['customData'].pop('availableEnd', None)
                    changed = True

                if item['customData'].get('availableStart', None) is not None:
                    item['customData'].pop('availableStart', None)
                    changed = True

                if item['customData'].get('realCostUSD', None) is not None:
                    item['customData'].pop('realCostUSD', None)
                    changed = True

                if item['customData'].get('classification', None) is not None:
                    item['customData'].pop('classification', None)
                    changed = True

                if item['customData'].get('isLocked', None) is not None:
                    item['customData'].pop('isLocked', None)
                    changed = True

                if item['customData'].get('isHidden', None) is not None:
                    item['customData'].pop('isHidden', None)
                    changed = True

                if item['customData'].get('category', None) is not None:
                    item['customData'].pop('category', None)
                    changed = True
                    
                if item['customData'].get('subcategory', None) is not None:
                    item['customData'].pop('subcategory', None)
                    changed = True

                if item['customData'].get('source', None) is not None:
                    item['customData'].pop('source', None)
                    changed = True

                if not changed:
                    continue

                print(f'Original item, {original_item}')
                print(f'Changed item, {item}')
                print()
                changed_purchases.append(item)
                break

    print(f'Changed items, {len(changed_purchases)}')
    for item in changed_purchases:
        if not print_only:
            edit_item(item)
        
if __name__ == '__main__':

    # list_player_inventory('23927952113485290', delete_all=False, delete_duplicates=True)

    # sign_up('6870324116313801')
    # add_player_inventory('7544096619038610', 'RUSTY_INV')
    # remove_player_inventory('7544096619038610', 'SAMURAIBUNDLE_INV')

    # change_user_balance('7544096619038610', 3000)
    # list_all_economy_resources()

    # push_dev_inventory_to_prod(print_only=False)

    inventory = [{"id": "cavelady", "name": "Cave Lady", "level": 0, "type": "avatar", "childKey": "cavelady"},
                 {"id": "defaultavatardark", "name": "Guy", "level": 0, "type": "avatar", "childKey": "defaultavatardark"},
                 {"id": "defaultavatarlight", "name": "Blockman", "level": 0, "type": "avatar", "childKey": "defaultavatarlight"},
                 {"id": "discordsword", "name": "Discord Sword", "level": 0, "type": "weapon", "childKey": "NJIUYT"},
                 {"id": "tiktokpick", "name": "Tiktock Pick", "level": 0, "type": "weapon", "childKey": "OFRNS8"},
                 {"id": "buildzone", "name": "Build Zone", "level": 0, "type": "buildzone"},
                 {"id": "blockplacer", "name": "Block Placer", "level": 0, "type": "blockplacer"},
                 {"id": "microphone", "name": "Microphone", "level": 0, "type": "weapon"},
                 {"id": "moderationstick", "name": "Moderation Stick", "level": 0, "type": "weapon"},
                 {"id": "pickaxe", "name": "Pickaxe", "level": 0, "type": "weapon"},
                 {"id": "webshooter", "name": "Web Shooter", "level": 0, "type": "gun"},
                 {"id": "redmonkey", "name": "Red Monkey", "level": 0, "type": "avatar"},
                 {"id": "sheetghost", "name": "Sheet Ghost", "level": 0, "type": "avatar"},
                 {"id": "axolotl", "name": "Axolotl", "level": 0, "type": "avatar"},
                 {"id": "femalehidetunic_suit", "name": "Female Tunic", "level": 0, "type": "suit", "childKey": "cavelady"},
                 {"id": "featherheadband_hat", "name": "Tribal Headband", "level": 0, "type": "hat", "childKey": "cavelady"},
                 {"id": "malehidetunic_suit", "name": "Male Tunic", "level": 0, "type": "suit", "childKey": "caveman"},
                 {"id": "purpleshirt_suit", "name": "Purple Shirt", "level": 0, "type": "suit", "childKey": "defaultavatardark"},
                 {"id": "greenshirt_suit", "name": "Green Shirt", "level": 0, "type": "suit", "childKey": "defaultavatarlight"},
                 {"id": "skirt1_suit", "name": "Skirt", "level": 0, "type": "suit", "childKey": "witch_bundle"},
                 {"id": "medievalarmour1_suit", "name": "Armor Suit", "level": 0, "type": "suit"},
                 {"id": "defaultavatarlight_hat", "name": "Blue Baseball Cap", "level": 0, "type": "hat", "childKey": "defaultavatarlight"},
                 {"id": "defaultavatardark_hat", "name": "Orange Baseball Cap", "level": 0, "type": "hat", "childKey": "defaultavatardark"},
                 {"id": "caveman", "name": "Cave Dweller", "level": 1, "type": "avatar", "childKey": "caveman"},
                 {"id": "sharktooth", "name": "Shark Tooth", "level": 1, "type": "weapon"},
                 {"id": "redpanda", "name": "Red Panda", "level": 1, "type": "avatar"},
                 {"id": "caveman_hat", "name": "Bone Helmet", "level": 1, "type": "hat", "childKey": "caveman"},

                 {"id": "jester", "name": "Jester", "level": 2, "type": "avatar", "childKey": "jester"},
                 {"id": "axolotlwing", "name": "Axolotl Wings", "level": 2, "type": "wing"},
                 {"id": "lemur", "name": "Lemur", "level": 2, "type": "avatar"},
                 {"id": "panda", "name": "Panda", "level": 2, "type": "avatar"},
                 {"id": "jester_hat", "name": "Jester Hat", "level": 2, "type": "hat", "childKey": "jester"},
                 {"id": "jester_suit", "name": "Jester Suit", "level": 2, "type": "suit", "childKey": "jester"},
                 {"id": "prisoner_suit", "name": "Prison Jumpsuit", "level": 2, "type": "suit", "childKey": "prisoner_outfit"},
                 {"id": "prisoner_hat", "name": "Prison Hat", "level": 2, "type": "hat", "childKey": "prisoner_outfit"},
                 {"id": "whitewalkerlemur", "name": "Snow Lemur", "level": 3, "type": "avatar", "childKey": "WINLEM"},
                 {"id": "vacuumcleaner", "name": "Vacuum Cleaner", "level": 3, "type": "broom"},
                 {"id": "breadhammer", "name": "Bread Head", "level": 3, "type": "weapon"},
                 {"id": "potato", "name": "Potato", "level": 3, "type": "avatar"},
                 {"id": "trashy", "name": "Trashy", "level": 3, "type": "avatar"},

                 {"id": "ubot", "name": "UBot", "level": 4, "type": "avatar", "childKey": "ASDPOI"},
                 {"id": "scarylemur", "name": "Devil Lemur", "level": 4, "type": "avatar", "childKey": "HALLEM"},
                 {"id": "broomstick", "name": "Flying Broom", "level": 4, "type": "broom", "childKey": "witch_bundle"},
                 {"id": "frog", "name": "Frog", "level": 4, "type": "avatar"},
                 {"id": "fork", "name": "Fork", "level": 4, "type": "weapon"},
                 {"id": "pizzaaxe", "name": "Pizza Axe", "level": 4, "type": "weapon"},
                 {"id": "firework", "name": "Firework", "level": 4, "type": "broom"},
                 {"id": "sword", "name": "Hofund", "level": 4, "type": "weapon"},
                 {"id": "goopy", "name": "Goopy", "level": 4, "type": "avatar"},
                 {"id": "polly", "name": "Polly", "level": 4, "type": "avatar"},
                 {"id": "catwoman", "name": "Catania", "level": 4, "type": "avatar"},
                 {"id": "nurse_suit", "name": "Scrubs", "level": 4, "type": "suit", "childKey": "nurse_outfit"},
                 {"id": "nurse_hat", "name": "Nurse Hat", "level": 4, "type": "hat", "childKey": "nurse_outfit"},

                 {"id": "gorilla", "name": "King Gorilla", "level": 5, "type": "avatar"},
                 {"id": "fryingpan", "name": "Zombie Slayer", "level": 5, "type": "weapon"},
                 {"id": "grapplegun", "name": "Grapple Gun", "level": 5, "type": "gun"},
                 {"id": "plunger", "name": "Plunger", "level": 5, "type": "weapon"},
                 {"id": "bonewing", "name": "Bone Wings", "level": 5, "type": "wing"},
                 {"id": "lizard", "name": "Reptilian", "level": 5, "type": "avatar"},
                 {"id": "monstermummy", "name": "Monster Mummy", "level": 5, "type": "avatar"},


                 {"id": "knife", "name": "Knife", "level": 6, "type": "weapon", "childKey": "horror_bundle"},
                 {"id": "skele", "name": "Skele", "level": 6, "type": "avatar", "childKey": "skele_bundle"},
                 {"id": "boneclub", "name": "Bone Club", "level": 6, "type": "weapon", "childKey": "skele_bundle"},
                 {"id": "pandy", "name": "Pandy", "level": 6, "type": "avatar"},
                 {"id": "snowman", "name": "Snow Man", "level": 6, "type": "avatar"},
                 {"id": "baseballbat", "name": "Baseballbat", "level": 6, "type": "weapon"},
                 {"id": "jesterwing", "name": "Jester Wings", "level": 6, "type": "wing"},
                 {"id": "britishsoldier_hat", "name": "British Soldier Hat", "level": 6, "type": "hat", "childKey": "britishsoldier_outfit"},
                 {"id": "britishsoldier_suit", "name": "British Soldier Suit", "level": 6, "type": "suit", "childKey": "britishsoldier_outfit"},
                 {"id": "doctor_suit", "name": "Doctor Coat", "level": 6, "type": "suit", "childKey": "doctor_outfit"},
                 {"id": "doctor_hat", "name": "Head Mirror", "level": 6, "type": "hat", "childKey": "doctor_outfit"},
                 {"id": "catman", "name": "Meowscles", "level": 7, "type": "avatar", "childKey": "catman"},
                 {"id": "squidhead", "name": "Squidhead", "level": 7, "type": "avatar", "childKey": "squid_bundle"},
                 {"id": "rhino", "name": "Rhino", "level": 7, "type": "avatar"},
                 {"id": "ogre", "name": "Ogre", "level": 7, "type": "avatar"},
                 {"id": "walrus", "name": "Walrus", "level": 7, "type": "avatar"},
                 {"id": "rockgolem1", "name": "Rock Golem Black", "level": 7, "type": "avatar"},
                 {"id": "rockgolem2", "name": "Rock Golem White", "level": 7, "type": "avatar"},
                 {"id": "banjo", "name": "Banjo", "level": 7, "type": "weapon"},
                 {"id": "jet", "name": "Rocket", "level": 7, "type": "broom"},
                 {"id": "crystalhammer", "name": "Crystal Hammer", "level": 7, "type": "weapon"},
                 {"id": "rusty", "name": "Rusty", "level": 7, "type": "avatar"},
                 {"id": "devilwing", "name": "Devil Wings", "level": 7, "type": "wing"},
                 {"id": "flamingbook_hat", "name": "Book Hat", "level": 7, "type": "hat"},
                 {"id": "rusty_suit", "name": "Rusty Skin", "level": 7, "type": "suit"},
                 {"id": "catmanmuscles_suit", "name": "Cat Muscle Suit", "level": 7, "type": "suit", "childKey": "catman"},


                 {"id": "easterbunny", "name": "Easter Bunny", "level": 8, "type": "avatar", "childKey": "easterbunny_bundle"},
                 {"id": "katana", "name": "Katana", "level": 8, "type": "weapon", "childKey": "samurai_bundle"},
                 {"id": "pebble", "name": "Pebble", "level": 8, "type": "avatar"},
                 {"id": "stonewing", "name": "Stone Wings", "level": 8, "type": "wing"},
                 {"id": "scepter", "name": "Scepter", "level": 8, "type": "weapon"},
                 {"id": "astronauthelmet_hat", "name": "Astronaut Helmet", "level": 8, "type": "hat", "childKey": "astronaut_outfit"},
                 {"id": "astronaut_suit", "name": "Space Suit", "level": 8, "type": "suit", "childKey": "astronaut_outfit"},
                 {"id": "easterbunny_suit", "name": "Overall", "level": 8, "type": "suit", "childKey": "easterbunny_bundle"},
                 {"id": "seacaptain_hat", "name": "Captain Hat", "level": 8, "type": "hat", "childKey": "sailor_outfit"},
                 {"id": "seacaptain_suit", "name": "Captain Suit", "level": 8, "type": "suit", "childKey": "sailor_outfit"},
                 {"id": "sheriff_suit", "name": "Sheriff Jacket", "level": 8, "type": "suit", "childKey": "sheriff_outfit"},
                 {"id": "sheriff_hat", "name": "Sheriff Hat", "level": 8, "type": "hat", "childKey": "sheriff_outfit"},
                 {"id": "largechokuto", "name": "Chokuto", "level": 9, "type": "weapon", "childKey": "armoured_samurai_bundle"},
                 {"id": "fox", "name": "Fox", "level": 9, "type": "avatar", "childKey": "fox_bundle"},
                 {"id": "zombie", "name": "Zombie", "level": 9, "type": "avatar", "childKey": "zombie"},
                 {"id": "gorillatree", "name": "Gorilla Tree", "level": 9, "type": "avatar"},
                 {"id": "sphynx", "name": "Sphynx", "level": 9, "type": "avatar"},
                 {"id": "grenadelauncher", "name": "Grenade Launcher", "level": 9, "type": "gun"},
                 {"id": "archer_hat", "name": "Archer Hat", "level": 9, "type": "hat", "childKey": "fox_bundle"},
                 {"id": "archer_suit", "name": "Archer Suit", "level": 9, "type": "suit", "childKey": "fox_bundle"},
                 {"id": "zombieshirt_suit", "name": "Zombie Shirt", "level": 9, "type": "suit", "childKey": "zombie"},


                 {"id": "nimbus", "name": "Nimbus", "level": 10, "type": "broom"},
                 {"id": "angelwing", "name": "Angel Wings", "level": 10, "type": "wing"},
                 {"id": "medievalhelmet2_hat", "name": "Armor Helmet", "level": 10, "type": "hat", "childKey": "medievalarmor_outfit"},
                 {"id": "medievalarmour2_suit", "name": "Armor Suit", "level": 10, "type": "suit", "childKey": "medievalarmor_outfit"},
                 {"id": "police_suit", "name": "Police Jacket", "level": 10, "type": "suit", "childKey": "police_outfit"},
                 {"id": "police_hat", "name": "Police Hat", "level": 10, "type": "hat", "childKey": "police_outfit"},
                 {"id": "samurai", "name": "Samurai", "level": 11, "type": "avatar", "childKey": "samurai_bundle"},
                 {"id": "beastbuck", "name": "Baggo", "level": 11, "type": "avatar"},
                 {"id": "bubbles", "name": "Bubbles", "level": 11, "type": "avatar"},
                 {"id": "trident", "name": "Trident", "level": 11, "type": "weapon"},
                 {"id": "kimono_suit", "name": "Kimono", "level": 11, "type": "suit", "childKey": "samurai_bundle"},
                 {"id": "bestbuck_suit", "name": "Coin Bag", "level": 11, "type": "suit"},


                 {"id": "dragonwing", "name": "Dragon Wings", "level": 12, "type": "wing", "childKey": "dragon_bundle"},
                 {"id": "tailoredsuit_suit", "name": "Tailored Suit", "level": 12, "type": "suit", "childKey": "tailoredsuit_outfit"},
                 {"id": "camera_hat", "name": "Camera Head", "level": 12, "type": "hat", "childKey": "tailoredsuit_outfit"},
                 {"id": "tophat_hat", "name": "Top Hat", "level": 12, "type": "hat"},
                 {"id": "scarecrow_suit", "name": "Scarecrow Suit", "level": 12, "type": "suit"},
                 {"id": "pumpkinhead_hat", "name": "Pumpkin Head", "level": 12, "type": "hat", "childKey": "scarecrow_outfit"},
                 {"id": "scarecrow_suit", "name": "Scarecrow Suit", "level": 12, "type": "suit", "childKey": "scarecrow_outfit"},
                 {"id": "sharkilla", "name": "Sharkilla", "level": 13, "type": "avatar", "childKey": "sharkilla"},
                 {"id": "skeletonwitch", "name": "Skeleton Witch", "level": 13, "type": "avatar", "childKey": "witch_bundle"},
                 {"id": "axe", "name": "Stormcrusher", "level": 13, "type": "weapon"},
                 {"id": "sharkillamuscles_suit", "name": "Muscle Suit", "level": 13, "type": "suit", "childKey": "sharkilla"},
                 {"id": "witch_hat", "name": "Witch Hat", "level": 13, "type": "hat", "childKey": "witch_bundle"},
                 {"id": "discomonkey", "name": "Disco Monkey", "level": 14, "type": "avatar", "childKey": "discomonkey_bundle"},
                 {"id": "deadfish", "name": "Dead Fish", "level": 14, "type": "weapon", "childKey": "squid_bundle"},
                 {"id": "rainbowafro_hat", "name": "Rainbow Afro", "level": 14, "type": "hat", "childKey": "discomonkey_bundle"},
                 {"id": "peasantwear_suit", "name": "Disco Suit", "level": 14, "type": "suit", "childKey": "discomonkey_bundle"},
                 {"id": "dragonman", "name": "Dragon", "level": 15, "type": "avatar", "childKey": "dragon_bundle"},
                 {"id": "hammer", "name": "Thor's Hammer", "level": 15, "type": "weapon"},
                 {"id": "dragonmanmuscles_suit", "name": "Dragon Muscles", "level": 15, "type": "suit", "childKey": "dragon_bundle"},
                 {"id": "armouredsamurai", "name": "Ninja", "level": 16, "type": "avatar", "childKey": "armoured_samurai_bundle"},
                 {"id": "samuraihelmet_hat", "name": "Samurai Helmet", "level": 16, "type": "hat", "childKey": "armoured_samurai_bundle"},
                 {"id": "samuraiarmour_suit", "name": "Samurai Armor", "level": 16, "type": "suit", "childKey": "armoured_samurai_bundle"},
                 {"id": "kinglion", "name": "King Lion", "level": 17, "type": "avatar", "childKey": "kinglion"},
                 {"id": "crown_hat", "name": "Crown", "level": 17, "type": "hat", "childKey": "kinglion"},
                 {"id": "kingclothes_suit", "name": "King Robes", "level": 17, "type": "suit", "childKey": "kinglion"},

                 {"id": "haunteddoll", "name": "Haunted Doll", "level": 18, "type": "avatar", "childKey": "horror_bundle"},
                 {"id": "tornshirt_suit", "name": "Torn Shirt", "level": 18, "type": "suit", "childKey": "horror_bundle"},
                 {"id": "bloodykatana", "name": "Bloody Katana", "level": 20, "type": "weapon"},















                 {"id": "HALLEM", "name": "Halloween Reward", "type": "promo code", "childKey": "HALLEM"},
                 {"id": "NJIUYT", "name": "Discord Reward", "type": "promo code", "childKey": "NJIUYT"},
                 {"id": "OFRNS8", "name": "TikTok Reward", "type": "promo code", "childKey": "OFRNS8"},
                 {"id": "WINLEM", "name": "Winter Reward", "type": "promo code", "childKey": "WINLEM"},

                 {"id": "ASDPOI", "name": "Youtube Reward", "type": "promo code", "childKey": "ASDPOI"},]
    virtual_purchases = [{"id": "cavelady", "name": "Cave Lady", "amount": 350, "group": 0, "isParent": "TRUE", "childKey": "cavelady", "type": "simple"},
                         {"id": "defaultavatardark", "name": "Guy", "amount": 350, "group": 0, "isParent": "TRUE", "childKey": "defaultavatardark", "type": "simple"},
                         {"id": "defaultavatarlight", "name": "Blockman", "amount": 350, "group": 0, "isParent": "TRUE", "childKey": "defaultavatarlight", "type": "simple"},
                         {"id": "discordsword", "name": "Discord Sword", "group": 0, "isLocked": "TRUE", "childKey": "NJIUYT", "type": "simple"},
                         {"id": "tiktokpick", "name": "Tiktock Pick", "group": 0, "isLocked": "TRUE", "childKey": "OFRNS8", "description": "Unlock this item by subscribing to Animal Blocks TikTok channel!", "type": "simple"},
                         {"id": "buildzone", "name": "Build Zone", "group": 0, "type": "simple"},
                         {"id": "blockplacer", "name": "Block Placer", "group": 0, "type": "simple"},
                         {"id": "microphone", "name": "Microphone", "amount": 350, "group": 0, "type": "simple"},
                         {"id": "moderationstick", "name": "Moderation Stick", "group": -1, "type": "simple"},
                         {"id": "pickaxe", "name": "Pickaxe", "group": 0, "type": "simple"},
                         {"id": "webshooter", "name": "Web Shooter", "amount": 350, "group": 0, "type": "simple"},
                         {"id": "redmonkey", "name": "Red Monkey", "group": 0, "type": "simple"},
                         {"id": "sheetghost", "name": "Sheet Ghost", "amount": 350, "group": 0, "type": "simple"},
                         {"id": "axolotl", "name": "Axolotl", "amount": 350, "group": 0, "type": "simple"},









                         {"id": "caveman", "name": "Cave Dweller", "amount": 700, "group": 0, "isParent": "TRUE", "childKey": "caveman", "type": "simple"},
                         {"id": "sharktooth", "name": "Shark Tooth", "amount": 700, "group": 0, "type": "simple"},
                         {"id": "redpanda", "name": "Red Panda", "amount": 700, "group": 0, "type": "simple"},

                         {"id": "prisoner_outfit", "name": "Prisoner Uniform", "amount": 1050, "group": 0, "isParent": "TRUE", "childKey": "prisoner_outfit", "type": "outfit"},
                         {"id": "jester", "name": "Jester", "group": 0, "isLocked": "TRUE", "isParent": "TRUE", "childKey": "jester", "type": "simple"},
                         {"id": "axolotlwing", "name": "Axolotl Wings", "amount": 1050, "group": 0, "type": "simple"},
                         {"id": "lemur", "name": "Lemur", "amount": 1050, "group": 0, "type": "simple"},
                         {"id": "panda", "name": "Panda", "group": 0, "isLocked": "TRUE", "type": "simple"},




                         {"id": "whitewalkerlemur", "name": "Snow Lemur", "group": 0, "isLocked": "TRUE", "childKey": "WINLEM", "description": "Winter Promo Skin", "type": "simple"},
                         {"id": "vacuumcleaner", "name": "Vacuum Cleaner", "amount": 1400, "group": 0, "type": "simple"},
                         {"id": "breadhammer", "name": "Bread Head", "amount": 1400, "group": 0, "type": "simple"},
                         {"id": "potato", "name": "Potato", "amount": 1400, "group": 0, "type": "simple"},
                         {"id": "trashy", "name": "Trashy", "amount": 1400, "group": 0, "type": "simple"},
                         {"id": "nurse_outfit", "name": "Nurse Uniform", "amount": 1750, "group": 0, "isParent": "TRUE", "childKey": "nurse_outfit", "type": "outfit"},
                         {"id": "ubot", "name": "UBot", "group": 0, "isLocked": "TRUE", "childKey": "ASDPOI", "description": "Become a Animal Blocks influencer to get the UBot!", "type": "simple"},
                         {"id": "scarylemur", "name": "Devil Lemur", "group": 0, "isLocked": "TRUE", "childKey": "HALLEM", "description": "The Devil Lemur is a Halloween item! ", "type": "simple"},
                         {"id": "broomstick", "name": "Flying Broom", "group": 0, "isLocked": "TRUE", "childKey": "witch_bundle", "description": "Unlock the broom when you buy the Broom Bundle!", "type": "simple"},
                         {"id": "frog", "name": "Frog", "amount": 1750, "group": 0, "type": "simple"},
                         {"id": "fork", "name": "Fork", "amount": 1750, "group": 0, "type": "simple"},
                         {"id": "pizzaaxe", "name": "Pizza Axe", "amount": 1750, "group": 0, "type": "simple"},
                         {"id": "firework", "name": "Firework", "amount": 1750, "group": 0, "type": "simple"},
                         {"id": "sword", "name": "Hofund", "group": 0, "isLocked": "TRUE", "type": "simple"},
                         {"id": "goopy", "name": "Goopy", "amount": 1750, "group": 0, "type": "simple"},
                         {"id": "polly", "name": "Polly", "group": 0, "isLocked": "TRUE", "type": "simple"},
                         {"id": "catwoman", "name": "Catania", "amount": 1750, "group": 0, "type": "simple"},


                         {"id": "astronaut_outfit", "name": "Astronaut Outfit", "amount": 2100, "isParent": "TRUE", "childKey": "astronaut_outfit", "type": "outfit"},
                         {"id": "gorilla", "name": "King Gorilla", "amount": 2100, "group": 0, "type": "simple"},
                         {"id": "fryingpan", "name": "Zombie Slayer", "amount": 2100, "group": 0, "type": "simple"},
                         {"id": "grapplegun", "name": "Grapple Gun", "amount": 2100, "group": 0, "type": "simple"},
                         {"id": "plunger", "name": "Plunger", "amount": 2100, "group": 0, "type": "simple"},
                         {"id": "bonewing", "name": "Bone Wings", "amount": 2100, "group": 0, "type": "simple"},
                         {"id": "lizard", "name": "Reptilian", "amount": 2100, "group": 0, "type": "simple"},
                         {"id": "monstermummy", "name": "Monster Mummy", "amount": 2100, "group": 0, "type": "simple"},
                         {"id": "britishsoldier_outfit", "name": "British Soldier Outfit", "amount": 2450, "group": 0, "isParent": "TRUE", "childKey": "britishsoldier_outfit", "type": "outfit"},
                         {"id": "doctor_outfit", "name": "Doctor Uniform", "amount": 2450, "group": 0, "isParent": "TRUE", "childKey": "doctor_outfit", "type": "outfit"},
                         {"id": "knife", "name": "Knife", "group": 0, "isLocked": "TRUE", "childKey": "horror_bundle", "description": "Get the knife when you buy the Horror Bundle", "type": "simple"},
                         {"id": "skele", "name": "Skele", "group": 0, "isLocked": "TRUE", "isParent": "TRUE", "childKey": "skele_bundle", "description": "Become Skele when you buy the Scele bundle!", "type": "simple"},
                         {"id": "boneclub", "name": "Bone Club", "group": 0, "isLocked": "TRUE", "childKey": "skele_bundle", "type": "simple"},
                         {"id": "pandy", "name": "Pandy", "amount": 2450, "group": 0, "type": "simple"},
                         {"id": "snowman", "name": "Snow Man", "amount": 2450, "group": 0, "type": "simple"},
                         {"id": "baseballbat", "name": "Baseballbat", "amount": 2450, "group": 0, "type": "simple"},
                         {"id": "jesterwing", "name": "Jester Wings", "amount": 2450, "group": 0, "type": "simple"},




                         {"id": "catman", "name": "Meowscles", "amount": 2800, "group": 0, "isParent": "TRUE", "childKey": "catman", "type": "simple"},
                         {"id": "squidhead", "name": "Squidhead", "group": 0, "isLocked": "TRUE", "isParent": "TRUE", "childKey": "squid_bundle", "description": "Become Squidhead when you buy the Squid Bundle!", "type": "simple"},
                         {"id": "rhino", "name": "Rhino", "amount": 2800, "group": 0, "type": "simple"},
                         {"id": "ogre", "name": "Ogre", "amount": 2800, "group": 0, "type": "simple"},
                         {"id": "walrus", "name": "Walrus", "amount": 2800, "group": 0, "type": "simple"},
                         {"id": "rockgolem1", "name": "Rock Golem Black", "group": -1, "isLocked": "TRUE", "type": "simple"},
                         {"id": "rockgolem2", "name": "Rock Golem White", "group": -1, "isLocked": "TRUE", "type": "simple"},
                         {"id": "banjo", "name": "Banjo", "amount": 2800, "group": 0, "type": "simple"},
                         {"id": "jet", "name": "Rocket", "amount": 2800, "group": 0, "type": "simple"},
                         {"id": "crystalhammer", "name": "Crystal Hammer", "amount": 2800, "group": 0, "type": "simple"},
                         {"id": "rusty", "name": "Rusty", "group": -1, "isLocked": "TRUE", "type": "simple"},
                         {"id": "devilwing", "name": "Devil Wings", "amount": 2800, "group": 0, "type": "simple"},
                         {"id": "flamingbook_hat", "name": "Book Hat", "group": -1, "isLocked": "TRUE", "type": "simple"},
                         {"id": "rusty_suit", "name": "Rusty Skin", "group": -1, "isLocked": "TRUE", "type": "simple"},

                         {"id": "sailor_outfit", "name": "Sailor Outfit", "amount": 3150, "group": 0, "isParent": "TRUE", "childKey": "sailor_outfit", "type": "outfit"},
                         {"id": "sheriff_outfit", "name": "Sheriff Uniform", "amount": 3150, "group": 0, "isParent": " TRUE", "childKey": "sheriff_outfit", "type": "outfit"},
                         {"id": "easterbunny", "name": "Easter Bunny", "group": 0, "isLocked": "TRUE", "isParent": "TRUE", "childKey": "easterbunny_bundle", "description": "Become the Bunny when you buy the Easter Bunny Bundle!", "type": "simple"},
                         {"id": "katana", "name": "Katana", "group": 0, "isLocked": "TRUE", "childKey": "samurai_bundle", "type": "simple"},
                         {"id": "pebble", "name": "Pebble", "amount": 3150, "group": 0, "type": "simple"},
                         {"id": "stonewing", "name": "Stone Wings", "amount": 3150, "group": 0, "type": "simple"},
                         {"id": "scepter", "name": "Scepter", "amount": 3150, "group": 0, "type": "simple"},







                         {"id": "largechokuto", "name": "Chokuto", "group": 0, "isLocked": "TRUE", "childKey": "armoured_samurai_bundle", "type": "simple"},
                         {"id": "fox", "name": "Fox", "group": 0, "isLocked": "TRUE", "isParent": "TRUE", "childKey": "fox_bundle", "description": "Get the Fox when you buy the Fox Bundle!", "type": "simple"},
                         {"id": "zombie", "name": "Zombie", "amount": 3500, "group": 0, "isParent": "TRUE", "childKey": "zombie", "type": "simple"},
                         {"id": "gorillatree", "name": "Gorilla Tree", "amount": 3500, "group": 0, "type": "simple"},
                         {"id": "sphynx", "name": "Sphynx", "amount": 3500, "group": 0, "type": "simple"},
                         {"id": "grenadelauncher", "name": "Grenade Launcher", "amount": 3500, "group": -1, "isLocked": "TRUE", "type": "simple"},



                         {"id": "medievalarmor_outfit", "name": "Medieval Armor Outfit", "amount": 3850, "group": -1, "isParent": "TRUE", "childKey": "medievalarmor_outfit", "type": "outfit"},
                         {"id": "police_outfit", "name": "Police Uniform", "amount": 3850, "group": 0, "isParent": "TRUE", "childKey": "police_outfit", "type": "outfit"},
                         {"id": "nimbus", "name": "Nimbus", "amount": 3850, "group": 0, "type": "simple"},
                         {"id": "angelwing", "name": "Angel Wings", "amount": 3850, "group": 0, "type": "simple"},




                         {"id": "samurai", "name": "Samurai", "group": 0, "isLocked": "TRUE", "isParent": "TRUE", "childKey": "samurai_bundle", "description": "Get the Samurai when you buy the Samurai Bundle!", "type": "simple"},
                         {"id": "beastbuck", "name": "Baggo", "amount": 4200, "group": 0, "type": "simple"},
                         {"id": "bubbles", "name": "Bubbles", "amount": 4200, "group": 0, "type": "simple"},
                         {"id": "trident", "name": "Trident", "amount": 4200, "group": 0, "type": "simple"},


                         {"id": "tailoredsuit_outfit", "name": "Camera Man Outfit", "amount": 4550, "isParent": "TRUE", "childKey": "tailoredsuit_outfit", "type": "outfit"},
                         {"id": "scarecrow_outfit", "name": "Scarecrow Outfit", "amount": 4550, "isParent": "TRUE", "childKey": "scarecrow_outfit", "type": "outfit"},
                         {"id": "dragonwing", "name": "Dragon Wings", "group": 0, "isLocked": "TRUE", "childKey": "dragon_bundle", "type": "simple"},






                         {"id": "sharkilla", "name": "Sharkilla", "amount": 4900, "group": 0, "isParent": "TRUE", "childKey": "sharkilla", "type": "simple"},
                         {"id": "skeletonwitch", "name": "Skeleton Witch", "group": 0, "isLocked": "TRUE", "isParent": "TRUE", "childKey": "witch_bundle", "description": "Become the Skeleton Witch when you buy the Witch Bundle!", "type": "simple"},
                         {"id": "axe", "name": "Stormcrusher", "amount": 4900, "group": 0, "type": "simple"},


                         {"id": "discomonkey", "name": "Disco Monkey", "group": 0, "isLocked": "TRUE", "isParent": "TRUE", "childKey": "discomonkey_bundle", "description": "Get the Monkey when you buy the Disco Monkey Bundle!", "type": "simple"},
                         {"id": "deadfish", "name": "Dead Fish", "group": 0, "isLocked": "TRUE", "childKey": "squid_bundle", "description": "Slap someone with this fish when you buy the Squid Bundle!", "type": "simple"},


                         {"id": "dragonman", "name": "Dragon", "group": 0, "isLocked": "TRUE", "isParent": "TRUE", "childKey": "dragon_bundle", "description": "Become the Dragon Man when you buy the Dragon Bundle!", "type": "simple"},
                         {"id": "hammer", "name": "Thor's Hammer", "amount": 5600, "group": 0, "type": "simple"},

                         {"id": "armouredsamurai", "name": "Ninja", "group": 0, "isLocked": "TRUE", "isParent": "TRUE", "childKey": "armoured_samurai_bundle", "description": "Get the Ninja when you buy the Armored Ninja Bundle", "type": "simple"},


                         {"id": "kinglion", "name": "King Lion", "amount": 6300, "group": -1, "isParent": "TRUE", "childKey": "kinglion", "type": "simple"},


                         {"id": "horror_bundle", "name": "Horror Bundle", "realCostUSD": 24.99, "bundleCoins": 10000, "isParent": "TRUE", "childKey": "horror_bundle", "description": "Buy this bundle to be a haunted doll, get a knife and 10K Coins!", "type": "bundle"},
                         {"id": "haunteddoll", "name": "Haunted Doll", "group": 0, "isLocked": "TRUE", "isParent": "TRUE", "childKey": "horror_bundle", "description": "Get the Haunted doll when you buy the Horror Bundle", "type": "simple"},

                         {"id": "bloodykatana", "name": "Bloody Katana", "amount": 7350, "group": 0, "type": "simple"},
                         {"id": "armoured_samurai_bundle", "name": "Armored Ninja Bundle", "availableStart": "2024-12-12T00:00:00+00:00", "availableEnd": "2026-12-12T00:00:00+00:00", "realCostUSD": 9.99, "bundleCoins": 2400, "isParent": "TRUE", "childKey": "armoured_samurai_bundle", "description": "Get the Ninja, Armor, Chokuto and 2400 Coins!", "type": "bundle"},
                         {"id": "discomonkey_bundle", "name": "Disco Monkey Bundle", "availableStart": "2024-12-12T00:00:00+00:00", "availableEnd": "2026-12-12T00:00:00+00:00", "realCostUSD": 24.99, "bundleCoins": 10000, "isParent": "TRUE", "childKey": "discomonkey_bundle", "description": "Get the Monkey, Rainbow afro and 10k Coins!", "type": "bundle"},
                         {"id": "dragon_bundle", "name": "Dragon Bundle", "availableStart": "2024-12-12T00:00:00+00:00", "availableEnd": "2026-12-12T00:00:00+00:00", "realCostUSD": 24.99, "bundleCoins": 10000, "isParent": "TRUE", "childKey": "dragon_bundle", "description": "Get the Dragon, dragon flex suit, dragon wings and 10k coins!", "type": "bundle"},
                         {"id": "easterbunny_bundle", "name": "Easter Bunny Bundle", "availableStart": "2024-12-12T00:00:00+00:00", "availableEnd": "2026-12-12T00:00:00+00:00", "realCostUSD": 9.99, "bundleCoins": 1000, "isParent": "TRUE", "childKey": "easterbunny_bundle", "description": "Get the Easter Bunny, Flower overall and 1k Coins!", "type": "bundle"},
                         {"id": "fox_bundle", "name": "Fox Bundle", "realCostUSD": 24.99, "bundleCoins": 10000, "isParent": "TRUE", "childKey": "fox_bundle", "description": "Get the Fox, Archer hat and suit and 10k Coins!", "type": "bundle"},
                         {"id": "kinglion_bundle", "name": "King Lion Bundle", "availableStart": "2025-05-12T00:00:00+00:00", "availableEnd": "2025-06-12T00:00:00+00:00", "realCostUSD": 24.99, "group": 0, "bundleCoins": 10000, "isParent": "TRUE", "childKey": "kinglion", "description": "Be the King Lion and get 10k Coins!", "type": "bundle"},
                         {"id": "samurai_bundle", "name": "Samurai Bundle", "realCostUSD": 24.99, "bundleCoins": 10000, "isParent": "TRUE", "childKey": "samurai_bundle", "description": "Buy this bundle to be a haunted doll, get a knife and 10K Coins!", "type": "bundle"},
                         {"id": "skele_bundle", "name": "Skele Bundle", "realCostUSD": 14.99, "bundleCoins": 5000, "isParent": "TRUE", "childKey": "skele_bundle", "description": "Buy this bundle to be Skele, get the Bone Club AND get 5k Coins!", "type": "bundle"},
                         {"id": "squid_bundle", "name": "Squid Bundle", "realCostUSD": 24.99, "bundleCoins": 10000, "isParent": "TRUE", "childKey": "squid_bundle", "description": "Buy this bundle to be Squidhead, slap someone with a dead fish AND get 10k Coins!", "type": "bundle"},
                         {"id": "witch_bundle", "name": "Witch Bundle", "realCostUSD": 24.99, "bundleCoins": 10000, "isParent": "TRUE", "childKey": "witch_bundle", "description": "Buy this bundle to be the Skeleton Witch, fly around with your broom AND get 10k Coins!", "type": "bundle"},
                         {"id": "PURCHASE_OPTION_5", "name": "Buy 1000 Coins", "bundleCoins": 1000, "type": "coin"},
                         {"id": "PURCHASE_OPTION_10", "name": "Buy 2400 Coins", "bundleCoins": 2400, "type": "coin"},
                         {"id": "PURCHASE_OPTION_20", "name": "Buy 7000 Coins", "bundleCoins": 7000, "type": "coin"},
                         {"id": "PURCHASE_OPTION_50", "name": "Buy 22000 Coins", "bundleCoins": 22000, "type": "coin"},
                         {"id": "PURCHASE_OPTION_100", "name": "Buy 50000 Coins", "bundleCoins": 50000, "type": "coin"},
                         {"id": "HALLEM", "name": "Halloween Reward", "isLocked": "TRUE", "childKey": "HALLEM", "type": "promo code"},
                         {"id": "NJIUYT", "name": "Discord Reward", "isLocked": "TRUE", "childKey": "NJIUYT", "type": "promo code"},
                         {"id": "OFRNS8", "name": "TikTok Reward", "isLocked": "TRUE", "childKey": "OFRNS8", "type": "promo code"},
                         {"id": "WINLEM", "name": "Winter Reward", "isLocked": "TRUE", "childKey": "WINLEM", "type": "promo code"},
                         {"id": "CLAIM_DAILY_REWARD", "name": "Daily Reward", "bundleCoins": 100, "isLocked": "TRUE", "type": "promo code"},
                         ]
    
    
    env = 'prod'

    if env == 'prod':
        ENVIRONMENT_ID = ENV_PROD
        ENVIRONMENT_NAME = ENV_PROD_NAME
    else:
        ENVIRONMENT_ID = ENV_DEV
        ENVIRONMENT_NAME = ENV_DEV_NAME
    
    # bulk_add_new_inventory_items(inventory, print_only=False)
    # bulk_add_type_to_inventory(inventory, print_only=False)
    # bulk_add_custom_data_to_existing_virtual_purchases(virtual_purchases, print_only=False)
    # bulk_add_new_virtual_purchases(virtual_purchases, inventory, print_only=False)

    # Do this step only after some time after release
    # bulk_remove_inv_from_inv_name(print_only=False)
    # bulk_edit_name('Beastcraft', 'Animal Blocks', print_only=False)
    bulk_edit_existing_virtual_purchase_rewards(virtual_purchases, inventory, print_only=True)
    # bulk_remove_custom_data_from_inventory(inventory, print_only=False)
    # bulk_remove_old_virtual_purchase_data(print_only=False)



